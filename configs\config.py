# Project Configuration

class Config:
    # Neo4j Database Configuration
    NEO4J_URI = "bolt://localhost:7687"
    NEO4J_USER = "neo4j"
    NEO4J_PASSWORD = "password"
    
    # Data Paths
    RAW_DATA_PATH = "data/raw"
    PROCESSED_DATA_PATH = "data/processed"
    GRAPHS_DATA_PATH = "data/graphs"
    
    # Schema Definition
    NODE_LABELS = {
        "Paper": ["title", "abstract", "arxiv_id", "published_date"],
        "Author": ["name", "affiliation"],
        "Topic": ["name", "category"],
        "Subject": ["name", "code"]
    }
    
    REL_TYPES = {
        "AUTHORED_BY": ["Paper", "Author"],
        "HAS_TOPIC": ["Paper", "Topic"],
        "BELONGS_TO": ["Paper", "Subject"],
        "COLLABORATES_WITH": ["Author", "Author"],
        "CITES": ["Paper", "Paper"]
    }
    
    # Data Mining Parameters
    CLUSTERING_MIN_SUPPORT = 0.1
    FSM_MIN_SUPPORT = 10
    GNN_HIDDEN_DIM = 64
    GNN_NUM_LAYERS = 2
    
    # Application Settings
    API_HOST = "127.0.0.1"
    API_PORT = 8000
    STREAMLIT_PORT = 8501
