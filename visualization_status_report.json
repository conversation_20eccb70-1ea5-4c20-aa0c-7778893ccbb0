{"timestamp": "2025-06-27T00:36:03.089352", "summary": {"total_expected": 8, "found_count": 7, "missing_count": 1, "completion_rate": 87.5}, "visualizations": {"enhanced_classification_results.png": {"exists": true, "file_size_kb": 475.7080078125, "description": "Enhanced multi-model classification comparison", "source_module": "src/data_mining/classification_enhanced.py", "priority": "HIGH"}, "frequent_subgraphs_gspan.png": {"exists": true, "file_size_kb": 339.556640625, "description": "gSpan frequent subgraph patterns", "source_module": "src/data_mining/association_rules_enhanced.py", "priority": "HIGH"}, "classification_result.png": {"exists": true, "file_size_kb": 355.3115234375, "description": "Original classification results visualization", "source_module": "src/data_mining/classification.py", "priority": "MEDIUM"}, "frequent_node_patterns.png": {"exists": true, "file_size_kb": 186.5244140625, "description": "Frequent node pattern analysis", "source_module": "src/data_mining/association_rules.py", "priority": "MEDIUM"}, "frequent_edge_patterns.png": {"exists": true, "file_size_kb": 251.669921875, "description": "Frequent edge pattern analysis", "source_module": "src/data_mining/association_rules.py", "priority": "MEDIUM"}, "author_collaboration_clusters.png": {"exists": true, "file_size_kb": 570.5908203125, "description": "Author collaboration network clustering", "source_module": "src/data_mining/clustering.py", "priority": "MEDIUM"}, "paper_topic_clusters.png": {"exists": true, "file_size_kb": 283.41796875, "description": "Paper-topic network clustering", "source_module": "src/data_mining/clustering.py", "priority": "MEDIUM"}, "font_test.png": {"exists": false, "file_size_kb": 0, "description": "Chinese font configuration test", "source_module": "src/utils/font_config.py", "priority": "LOW"}}}