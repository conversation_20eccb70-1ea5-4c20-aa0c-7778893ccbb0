"""
Auto-run Enhanced Knowledge Graph Mining
自动运行增强版本的知识图谱挖掘
"""

import os
import sys
from pathlib import Path
import time
import json

# Project setup
PROJECT_ROOT = Path(__file__).parent.absolute()
sys.path.insert(0, str(PROJECT_ROOT))

def check_dependencies():
    """检查关键依赖是否已安装"""
    print("Checking dependencies...")
    
    missing_deps = []
    
    try:
        import torch
        print(f"[OK] PyTorch: {torch.__version__}")
    except ImportError:
        missing_deps.append("torch")
    
    try:
        import torch_geometric
        print(f"[OK] PyTorch Geometric: {torch_geometric.__version__}")
    except ImportError:
        missing_deps.append("torch-geometric")
    
    try:
        import gspan_mining
        print("[OK] gspan-mining: Available")
    except ImportError:
        missing_deps.append("gspan-mining")
    
    try:
        import networkx as nx
        print(f"[OK] NetworkX: {nx.__version__}")
    except ImportError:
        missing_deps.append("networkx")
    
    try:
        import sklearn
        print(f"[OK] Scikit-learn: {sklearn.__version__}")
    except ImportError:
        missing_deps.append("scikit-learn")
    
    if missing_deps:
        print(f"\n[ERROR] Missing dependencies: {', '.join(missing_deps)}")
        print("Please install them using: pip install " + ' '.join(missing_deps))
        return False
    
    print("[OK] All dependencies are available!")
    return True

def run_enhanced_classification():
    """运行增强版分类任务"""
    print("\n" + "="*60)
    print("RUNNING ENHANCED GRAPH CLASSIFICATION")
    print("="*60)
    
    try:
        from src.data_mining.classification_enhanced import run_enhanced_classification
        
        start_time = time.time()
        classifier = run_enhanced_classification()
        end_time = time.time()
        
        if classifier:
            print(f"\n[OK] Enhanced classification completed in {end_time - start_time:.2f} seconds")
            return True
        else:
            print("[ERROR] Enhanced classification failed")
            return False
            
    except Exception as e:
        print(f"[ERROR] Error in enhanced classification: {e}")
        import traceback
        traceback.print_exc()
        return False

def run_enhanced_association_rules():
    """运行增强版关联规则挖掘"""
    print("\n" + "="*60)
    print("RUNNING ENHANCED ASSOCIATION RULES MINING")
    print("="*60)
    
    try:
        from src.data_mining.association_rules_enhanced import run_enhanced_gspan_mining
        
        start_time = time.time()
        miner = run_enhanced_gspan_mining()
        end_time = time.time()
        
        if miner:
            print(f"\n[OK] Enhanced association rules mining completed in {end_time - start_time:.2f} seconds")
            return True
        else:
            print("[ERROR] Enhanced association rules mining failed")
            return False
            
    except Exception as e:
        print(f"[ERROR] Error in enhanced association rules: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主运行函数"""
    print("Enhanced Knowledge Graph Mining System - Auto Run")
    print("="*60)
    
    # Check dependencies
    if not check_dependencies():
        return
    
    # Check if data exists
    data_file = PROJECT_ROOT / "data" / "processed" / "extracted_entities_relations.json"
    if not data_file.exists():
        print(f"\n[ERROR] Data file not found: {data_file}")
        print("Please ensure you have run the data extraction process first.")
        return
    
    print(f"\n[OK] Data file found: {data_file}")
    
    # Auto-run both enhanced methods
    print("\nAuto-running both enhanced methods...")
    
    # Run enhanced classification
    classification_success = run_enhanced_classification()
    
    # Run enhanced association rules
    association_success = run_enhanced_association_rules()
    
    # Summary
    print("\n" + "="*60)
    print("ENHANCED KNOWLEDGE GRAPH MINING COMPLETE!")
    print("="*60)
    
    if classification_success:
        print("[OK] Enhanced Classification: SUCCESS")
    else:
        print("[ERROR] Enhanced Classification: FAILED")
    
    if association_success:
        print("[OK] Enhanced Association Rules: SUCCESS")
    else:
        print("[ERROR] Enhanced Association Rules: FAILED")
    
    print("\nResults can be found in:")
    print("  [+] data/processed/ (JSON results)")
    print("  [+] data/graphs/ (Visualizations)")
    print("\nThe enhanced versions provide:")
    print("  [+] Better accuracy through advanced models")
    print("  [+] Deeper insights through rich features")
    print("  [+] True frequent subgraph mining")
    print("  [+] Professional analysis and reporting")

if __name__ == "__main__":
    main()
