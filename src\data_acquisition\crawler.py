"""
Data Acquisition Module
用于使用crawl4ai MCP抓取ArXiv论文数据
"""

import json
import re
import os
from typing import List, Dict, Optional
from datetime import datetime
import time


class ArxivCrawler:
    """ArXiv论文爬虫，使用crawl4ai MCP服务"""
    
    def __init__(self, output_dir: str = "data/raw"):
        self.output_dir = output_dir
        self.base_url = "https://arxiv.org/abs/"
        self.search_url = "https://arxiv.org/search/"
        
        # 确保输出目录存在
        os.makedirs(output_dir, exist_ok=True)
    
    def extract_paper_info(self, markdown_content: str, arxiv_id: str) -> Dict:
        """从crawl4ai抓取的markdown内容中提取论文信息"""
        paper_info = {
            "arxiv_id": arxiv_id,
            "title": "",
            "authors": [],
            "abstract": "",
            "subjects": [],
            "submitted_date": "",
            "url": f"https://arxiv.org/abs/{arxiv_id}",
            "crawled_at": datetime.now().isoformat()
        }
        
        lines = markdown_content.split('\n')
        
        # 提取标题
        for line in lines:
            if line.startswith('# ') and 'Title:' in line:
                title_match = re.search(r'Title:(.+)', line)
                if title_match:
                    paper_info["title"] = title_match.group(1).strip()
                break
        
        # 提取作者
        author_pattern = r'\[([^\]]+)\]\(https://arxiv\.org/search/[^)]+\)'
        authors = re.findall(author_pattern, markdown_content)
        paper_info["authors"] = [author.strip() for author in authors]
        
        # 提取摘要
        abstract_start = False
        abstract_lines = []
        for line in lines:
            if '> Abstract:' in line:
                abstract_start = True
                abstract_text = line.replace('> Abstract:', '').strip()
                if abstract_text:
                    abstract_lines.append(abstract_text)
                continue
            elif abstract_start and line.startswith('>'):
                abstract_lines.append(line[1:].strip())
            elif abstract_start and not line.startswith('>') and line.strip():
                break
        
        paper_info["abstract"] = ' '.join(abstract_lines).strip()
        
        # 提取学科分类
        subjects_pattern = r'Subjects:\s*\|\s*([^|]+)\s*\|'
        subjects_match = re.search(subjects_pattern, markdown_content)
        if subjects_match:
            subjects_text = subjects_match.group(1).strip()
            # 解析学科代码和名称
            subject_pattern = r'([^(]+)\s*\(([^)]+)\)'
            subject_matches = re.findall(subject_pattern, subjects_text)
            for name, code in subject_matches:
                paper_info["subjects"].append({
                    "name": name.strip(),
                    "code": code.strip()
                })
        
        # 提取提交日期
        date_pattern = r'\*\*\[v1\]\*\* \w+, (\d+ \w+ \d+)'
        date_match = re.search(date_pattern, markdown_content)
        if date_match:
            paper_info["submitted_date"] = date_match.group(1)
        
        return paper_info
    
    def save_paper_data(self, paper_info: Dict) -> str:
        """保存论文数据到JSON文件"""
        filename = f"{paper_info['arxiv_id'].replace('/', '_')}.json"
        filepath = os.path.join(self.output_dir, filename)
        
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(paper_info, f, ensure_ascii=False, indent=2)
        
        return filepath
    
    def crawl_paper(self, arxiv_id: str) -> Optional[Dict]:
        """
        爬取单篇论文
        注意：此方法需要调用crawl4ai MCP服务
        在实际使用时需要替换为MCP调用
        """
        url = f"{self.base_url}{arxiv_id}"
        
        # TODO: 这里需要调用crawl4ai MCP服务
        # 示例调用（需要在主程序中实现）：
        # result = crawl4ai_mcp.crawl(url)
        # markdown_content = result['markdown']
        
        print(f"需要调用crawl4ai MCP服务抓取: {url}")
        print("请在主程序中调用crawl4ai:md工具")
        
        return None
    
    def generate_arxiv_ids(self, start_year: int = 2023, 
                          categories: List[str] = None) -> List[str]:
        """生成要爬取的ArXiv ID列表"""
        if categories is None:
            categories = ["cs.AI", "cs.CL", "cs.LG", "cs.CV"]
        
        # 生成一些示例ID用于测试
        # 格式: YYMM.NNNNN
        sample_ids = [
            "2301.00001",  # 2023年1月
            "2301.00123",
            "2302.01234",  # 2023年2月
            "2303.02345",  # 2023年3月
            "2304.03456",  # 2023年4月
            "2305.04567",  # 2023年5月
        ]
        
        return sample_ids[:3]  # 先返回3个用于测试


def create_crawl_instruction():
    """创建爬取指令说明"""
    instructions = """
    ## 使用crawl4ai MCP抓取ArXiv数据的步骤：
    
    1. 获取要爬取的ArXiv ID列表：
       from src.data_acquisition.crawler import ArxivCrawler
       crawler = ArxivCrawler()
       arxiv_ids = crawler.generate_arxiv_ids()
    
    2. 对每个ArXiv ID调用crawl4ai MCP：
       for arxiv_id in arxiv_ids:
           url = f"https://arxiv.org/abs/{arxiv_id}"
           # 调用MCP工具：crawl4ai:md
           # 参数：url=url, f="fit"
           
    3. 处理抓取结果：
           paper_info = crawler.extract_paper_info(markdown_content, arxiv_id)
           crawler.save_paper_data(paper_info)
    
    示例MCP调用：
    - 工具名: crawl4ai:md
    - 参数: {"url": "https://arxiv.org/abs/2301.00001", "f": "fit"}
    """
    return instructions


if __name__ == "__main__":
    crawler = ArxivCrawler()
    print(create_crawl_instruction())
    
    # 生成要爬取的ID列表
    arxiv_ids = crawler.generate_arxiv_ids()
    print(f"\n要爬取的ArXiv IDs: {arxiv_ids}")
