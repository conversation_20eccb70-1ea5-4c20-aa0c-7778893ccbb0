{"method": "Enhanced Multi-Model GNN", "gnn_results": {"GraphSAGE": {"train_accuracy": 0.84375, "val_accuracy": 0.8333333333333334, "test_accuracy": 0.625, "classification_report": {"Author": {"precision": 1.0, "recall": 0.75, "f1-score": 0.8571428571428571, "support": 4.0}, "Paper": {"precision": 1.0, "recall": 1.0, "f1-score": 1.0, "support": 1.0}, "Subject": {"precision": 1.0, "recall": 0.3333333333333333, "f1-score": 0.5, "support": 3.0}, "Topic": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0.0}, "accuracy": 0.625, "macro avg": {"precision": 0.75, "recall": 0.5208333333333334, "f1-score": 0.5892857142857143, "support": 8.0}, "weighted avg": {"precision": 1.0, "recall": 0.625, "f1-score": 0.7410714285714286, "support": 8.0}}}, "GAT": {"train_accuracy": 0.5625, "val_accuracy": 0.6666666666666666, "test_accuracy": 0.375, "classification_report": {"Author": {"precision": 0.5, "recall": 0.75, "f1-score": 0.6, "support": 4.0}, "Paper": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 1.0}, "Subject": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 3.0}, "Topic": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0.0}, "accuracy": 0.375, "macro avg": {"precision": 0.125, "recall": 0.1875, "f1-score": 0.15, "support": 8.0}, "weighted avg": {"precision": 0.25, "recall": 0.375, "f1-score": 0.3, "support": 8.0}}}, "GCN": {"train_accuracy": 0.5, "val_accuracy": 0.6666666666666666, "test_accuracy": 0.625, "classification_report": {"Author": {"precision": 0.5714285714285714, "recall": 1.0, "f1-score": 0.7272727272727273, "support": 4.0}, "Paper": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 1.0}, "Subject": {"precision": 1.0, "recall": 0.3333333333333333, "f1-score": 0.5, "support": 3.0}, "Topic": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0.0}, "accuracy": 0.625, "macro avg": {"precision": 0.39285714285714285, "recall": 0.3333333333333333, "f1-score": 0.3068181818181818, "support": 8.0}, "weighted avg": {"precision": 0.6607142857142857, "recall": 0.625, "f1-score": 0.5511363636363636, "support": 8.0}}}}, "ml_results": {"Random Forest": {"test_accuracy": 1.0, "classification_report": {"Author": {"precision": 1.0, "recall": 1.0, "f1-score": 1.0, "support": 7.0}, "Paper": {"precision": 1.0, "recall": 1.0, "f1-score": 1.0, "support": 1.0}, "Subject": {"precision": 1.0, "recall": 1.0, "f1-score": 1.0, "support": 3.0}, "Topic": {"precision": 1.0, "recall": 1.0, "f1-score": 1.0, "support": 3.0}, "accuracy": 1.0, "macro avg": {"precision": 1.0, "recall": 1.0, "f1-score": 1.0, "support": 14.0}, "weighted avg": {"precision": 1.0, "recall": 1.0, "f1-score": 1.0, "support": 14.0}}}}, "best_model": "GraphSAGE", "best_accuracy": 0.625, "feature_engineering": {"num_features": 25, "feature_types": ["node_type_encoding", "centrality_measures", "neighbor_analysis", "structural_features", "path_features"]}, "graph_stats": {"num_nodes": 46, "num_edges": 117, "num_classes": 4}, "generated_at": "2025-06-26T22:38:46.457852"}