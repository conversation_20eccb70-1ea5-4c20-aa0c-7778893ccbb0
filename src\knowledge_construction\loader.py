"""
图数据库加载器
将提取的实体和关系加载到Neo4j数据库中
"""

import json
import os
from typing import Dict, List
from neo4j import GraphDatabase
import sys

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
from configs.config import Config


class Neo4jLoader:
    """Neo4j数据加载器"""
    
    def __init__(self, uri: str = None, user: str = None, password: str = None):
        self.uri = uri or Config.NEO4J_URI
        self.user = user or Config.NEO4J_USER
        self.password = password or Config.NEO4J_PASSWORD
        
        try:
            self.driver = GraphDatabase.driver(self.uri, auth=(self.user, self.password))
            # 测试连接
            with self.driver.session() as session:
                session.run("RETURN 1")
            print(f"成功连接到Neo4j数据库: {self.uri}")
        except Exception as e:
            print(f"连接Neo4j失败: {e}")
            print("请确保Neo4j服务已启动，运行: docker-compose up -d")
            raise
    
    def close(self):
        """关闭数据库连接"""
        if self.driver:
            self.driver.close()
    
    def clear_database(self):
        """清空数据库（谨慎使用）"""
        with self.driver.session() as session:
            session.run("MATCH (n) DETACH DELETE n")
        print("数据库已清空")
    
    def create_constraints_and_indexes(self):
        """创建约束和索引"""
        constraints_queries = [
            "CREATE CONSTRAINT paper_id IF NOT EXISTS FOR (p:Paper) REQUIRE p.arxiv_id IS UNIQUE",
            "CREATE CONSTRAINT author_id IF NOT EXISTS FOR (a:Author) REQUIRE a.name IS UNIQUE",
            "CREATE CONSTRAINT topic_id IF NOT EXISTS FOR (t:Topic) REQUIRE t.name IS UNIQUE",
            "CREATE CONSTRAINT subject_id IF NOT EXISTS FOR (s:Subject) REQUIRE s.code IS UNIQUE"
        ]
        
        with self.driver.session() as session:
            for query in constraints_queries:
                try:
                    session.run(query)
                    print(f"约束创建成功: {query.split()[-3]}")
                except Exception as e:
                    if "already exists" not in str(e):
                        print(f"创建约束失败: {e}")
    
    def load_papers(self, paper_entities: List[Dict]):
        """加载论文节点"""
        query = """
        UNWIND $papers as paper
        MERGE (p:Paper {arxiv_id: paper.properties.arxiv_id})
        SET p.title = paper.properties.title,
            p.abstract = paper.properties.abstract,
            p.published_date = paper.properties.published_date,
            p.url = paper.properties.url,
            p.loaded_at = datetime()
        """
        
        with self.driver.session() as session:
            result = session.run(query, papers=paper_entities)
            print(f"加载论文节点: {len(paper_entities)} 个")
    
    def load_authors(self, author_ids: List[str]):
        """加载作者节点"""
        authors_data = [{"name": author_id.replace('_', ' ').title(), "id": author_id} 
                       for author_id in author_ids]
        
        query = """
        UNWIND $authors as author
        MERGE (a:Author {name: author.id})
        SET a.display_name = author.name,
            a.loaded_at = datetime()
        """
        
        with self.driver.session() as session:
            session.run(query, authors=authors_data)
            print(f"加载作者节点: {len(author_ids)} 个")
    
    def load_topics(self, topic_ids: List[str]):
        """加载主题节点"""
        topics_data = [{"name": topic_id, "display_name": topic_id.replace('_', ' ').title()} 
                      for topic_id in topic_ids]
        
        query = """
        UNWIND $topics as topic
        MERGE (t:Topic {name: topic.name})
        SET t.display_name = topic.display_name,
            t.loaded_at = datetime()
        """
        
        with self.driver.session() as session:
            session.run(query, topics=topics_data)
            print(f"加载主题节点: {len(topic_ids)} 个")
    
    def load_subjects(self, subject_ids: List[str]):
        """加载学科节点"""
        # 这里需要从原始数据中获取学科的完整信息
        subjects_data = [{"code": subject_id, "name": subject_id} 
                        for subject_id in subject_ids]
        
        query = """
        UNWIND $subjects as subject
        MERGE (s:Subject {code: subject.code})
        SET s.name = subject.name,
            s.loaded_at = datetime()
        """
        
        with self.driver.session() as session:
            session.run(query, subjects=subjects_data)
            print(f"加载学科节点: {len(subject_ids)} 个")
    
    def load_relations(self, relations: List[Dict]):
        """加载关系"""
        # 按关系类型分组
        relations_by_type = {}
        for rel in relations:
            rel_type = rel["type"]
            if rel_type not in relations_by_type:
                relations_by_type[rel_type] = []
            relations_by_type[rel_type].append(rel)
        
        with self.driver.session() as session:
            # AUTHORED_BY关系
            if "AUTHORED_BY" in relations_by_type:
                query = """
                UNWIND $relations as rel
                MATCH (p:Paper {arxiv_id: rel.source})
                MATCH (a:Author {name: rel.target})
                MERGE (p)-[:AUTHORED_BY]->(a)
                """
                session.run(query, relations=relations_by_type["AUTHORED_BY"])
                print(f"加载AUTHORED_BY关系: {len(relations_by_type['AUTHORED_BY'])} 个")
            
            # HAS_TOPIC关系
            if "HAS_TOPIC" in relations_by_type:
                query = """
                UNWIND $relations as rel
                MATCH (p:Paper {arxiv_id: rel.source})
                MATCH (t:Topic {name: rel.target})
                MERGE (p)-[:HAS_TOPIC]->(t)
                """
                session.run(query, relations=relations_by_type["HAS_TOPIC"])
                print(f"加载HAS_TOPIC关系: {len(relations_by_type['HAS_TOPIC'])} 个")
            
            # BELONGS_TO关系
            if "BELONGS_TO" in relations_by_type:
                query = """
                UNWIND $relations as rel
                MATCH (p:Paper {arxiv_id: rel.source})
                MATCH (s:Subject {code: rel.target})
                MERGE (p)-[:BELONGS_TO]->(s)
                """
                session.run(query, relations=relations_by_type["BELONGS_TO"])
                print(f"加载BELONGS_TO关系: {len(relations_by_type['BELONGS_TO'])} 个")
            
            # COLLABORATES_WITH关系
            if "COLLABORATES_WITH" in relations_by_type:
                query = """
                UNWIND $relations as rel
                MATCH (a1:Author {name: rel.source})
                MATCH (a2:Author {name: rel.target})
                MERGE (a1)-[c:COLLABORATES_WITH]-(a2)
                SET c.via_paper = rel.via_paper
                """
                session.run(query, relations=relations_by_type["COLLABORATES_WITH"])
                print(f"加载COLLABORATES_WITH关系: {len(relations_by_type['COLLABORATES_WITH'])} 个")
    
    def load_all_data(self, data_file: str = "data/processed/extracted_entities_relations.json"):
        """加载所有数据到Neo4j"""
        print("开始加载数据到Neo4j...")
        
        # 读取提取的数据
        with open(data_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        # 创建约束和索引
        self.create_constraints_and_indexes()
        
        # 加载实体
        self.load_papers(data["paper_entities"])
        self.load_authors(data["entities"]["Author"])
        self.load_topics(data["entities"]["Topic"])
        self.load_subjects(data["entities"]["Subject"])
        
        # 加载关系
        self.load_relations(data["relations"])
        
        print("数据加载完成!")
        return True
    
    def get_database_stats(self):
        """获取数据库统计信息"""
        queries = {
            "papers": "MATCH (p:Paper) RETURN count(p) as count",
            "authors": "MATCH (a:Author) RETURN count(a) as count", 
            "topics": "MATCH (t:Topic) RETURN count(t) as count",
            "subjects": "MATCH (s:Subject) RETURN count(s) as count",
            "authored_by": "MATCH ()-[r:AUTHORED_BY]->() RETURN count(r) as count",
            "has_topic": "MATCH ()-[r:HAS_TOPIC]->() RETURN count(r) as count",
            "belongs_to": "MATCH ()-[r:BELONGS_TO]->() RETURN count(r) as count",
            "collaborates": "MATCH ()-[r:COLLABORATES_WITH]-() RETURN count(r) as count"
        }
        
        stats = {}
        with self.driver.session() as session:
            for name, query in queries.items():
                result = session.run(query)
                stats[name] = result.single()["count"]
        
        return stats
    
    def print_database_stats(self):
        """打印数据库统计信息"""
        stats = self.get_database_stats()
        
        print("\n=== Neo4j数据库统计 ===")
        print(f"论文数量: {stats['papers']}")
        print(f"作者数量: {stats['authors']}")
        print(f"主题数量: {stats['topics']}")
        print(f"学科数量: {stats['subjects']}")
        print(f"AUTHORED_BY关系: {stats['authored_by']}")
        print(f"HAS_TOPIC关系: {stats['has_topic']}")
        print(f"BELONGS_TO关系: {stats['belongs_to']}")
        print(f"COLLABORATES_WITH关系: {stats['collaborates']}")


def main():
    """主函数"""
    loader = Neo4jLoader()
    
    try:
        # 检查数据文件是否存在
        data_file = "data/processed/extracted_entities_relations.json"
        if not os.path.exists(data_file):
            print(f"数据文件不存在: {data_file}")
            print("请先运行实体关系提取: python src/knowledge_construction/extractor.py")
            return
        
        # 加载数据
        loader.load_all_data(data_file)
        
        # 显示统计信息
        loader.print_database_stats()
        
    finally:
        loader.close()


if __name__ == "__main__":
    main()
