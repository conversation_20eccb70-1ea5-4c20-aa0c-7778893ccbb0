#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
简化的数据挖掘运行脚本，避免特殊字符问题
"""

import os
import sys
from pathlib import Path
import time

# Project setup
PROJECT_ROOT = Path(__file__).parent.absolute()
sys.path.insert(0, str(PROJECT_ROOT))

def check_data_availability():
    """检查数据文件"""
    data_file = PROJECT_ROOT / "data" / "processed" / "extracted_entities_relations.json"
    if not data_file.exists():
        print(f"Data file not found: {data_file}")
        print("Please run data extraction first:")
        print("  python src/knowledge_construction/extractor.py")
        return False
    
    print(f"Data file found: {data_file}")
    return True

def run_fixed_classification():
    """运行修复后的分类模块"""
    print("\nRUNNING FIXED CLASSIFICATION MODULE")
    print("-" * 50)
    print("Fixes applied:")
    print("  - Proper train/test split BEFORE feature engineering")
    print("  - Centrality computed on training subgraph only")
    print("  - Test nodes use local features only")
    print()
    
    try:
        from src.data_mining.classification_enhanced_fixed import run_fixed_classification
        
        start_time = time.time()
        classifier = run_fixed_classification()
        duration = time.time() - start_time
        
        print(f"Classification completed in {duration:.2f} seconds")
        return True
        
    except ImportError as e:
        print(f"Import error: {e}")
        print("Fixed classification module not found")
        return False
    except Exception as e:
        print(f"Error running classification: {e}")
        return False

def run_fixed_association_rules():
    """运行修复后的关联规则模块"""
    print("\nRUNNING FIXED ASSOCIATION RULES MODULE")
    print("-" * 50)
    print("Fixes applied:")
    print("  - Corrected confidence calculation")
    print("  - All confidence values <= 1.0")
    print("  - Valid support and lift metrics")
    print()
    
    try:
        from src.data_mining.association_rules_enhanced_fixed import run_fixed_gspan_mining
        
        start_time = time.time()
        results = run_fixed_gspan_mining()
        duration = time.time() - start_time
        
        print(f"Association rules completed in {duration:.2f} seconds")
        return True
        
    except ImportError as e:
        print(f"Import error: {e}")
        print("Fixed association rules module not found")
        return False
    except Exception as e:
        print(f"Error running association rules: {e}")
        return False

def run_clustering():
    """运行聚类模块"""
    print("\nRUNNING CLUSTERING MODULE")
    print("-" * 50)
    print("Note: Clustering module was already working correctly")
    print()
    
    try:
        from src.data_mining.clustering import run_author_clustering, run_paper_topic_clustering
        
        start_time = time.time()
        
        # 运行作者聚类
        print("Running author clustering...")
        author_results = run_author_clustering()
        
        # 运行论文主题聚类
        print("Running paper-topic clustering...")
        topic_results = run_paper_topic_clustering()
        
        duration = time.time() - start_time
        
        print(f"Clustering completed in {duration:.2f} seconds")
        return True
        
    except ImportError as e:
        print(f"Import error: {e}")
        print("Clustering module not found")
        return False
    except Exception as e:
        print(f"Error running clustering: {e}")
        return False

def main():
    """主函数"""
    print("FIXED DATA MINING MODULES RUNNER")
    print("=" * 60)
    print("Running scientifically corrected versions")
    print("=" * 60)
    
    # 检查数据可用性
    if not check_data_availability():
        print("ERROR: Required data files not found")
        return 1
    
    results = {}
    
    # 运行修复后的模块
    print("\nStarting data mining analysis...")
    
    results['classification'] = run_fixed_classification()
    results['association_rules'] = run_fixed_association_rules()
    results['clustering'] = run_clustering()
    
    # 总结结果
    print("\n" + "=" * 60)
    print("EXECUTION SUMMARY")
    print("=" * 60)
    
    for module, success in results.items():
        status = "SUCCESS" if success else "FAILED"
        print(f"  {module.upper()}: {status}")
    
    successful_modules = sum(results.values())
    total_modules = len(results)
    
    print(f"\nModules completed successfully: {successful_modules}/{total_modules}")
    
    if successful_modules == total_modules:
        print("All modules executed successfully!")
        print("Check data/processed/ and data/graphs/ for results")
        return 0
    else:
        print("Some modules failed. Check error messages above.")
        return 1

if __name__ == "__main__":
    try:
        exit_code = main()
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\nOperation cancelled by user")
        sys.exit(1)
    except Exception as e:
        print(f"Unexpected error: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
