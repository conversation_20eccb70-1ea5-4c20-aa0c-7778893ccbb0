"""
Fix plt.show() Issue in Visualization Functions
修复可视化函数中的plt.show()问题，避免在无头环境中挂起
"""

import os
import re
from pathlib import Path

# Project setup
PROJECT_ROOT = Path(__file__).parent.absolute()

def find_plt_show_calls():
    """查找所有包含plt.show()的文件"""
    print("Searching for plt.show() calls in visualization modules...")
    
    # Files to check
    files_to_check = [
        "src/data_mining/clustering.py",
        "src/data_mining/classification.py", 
        "src/data_mining/association_rules.py",
        "src/data_mining/classification_enhanced.py",
        "src/data_mining/association_rules_enhanced.py",
        "src/utils/font_config.py"
    ]
    
    files_with_show = []
    
    for file_path in files_to_check:
        full_path = PROJECT_ROOT / file_path
        if not full_path.exists():
            continue
            
        with open(full_path, 'r', encoding='utf-8') as f:
            content = f.read()
            
        # Find plt.show() calls
        show_calls = re.findall(r'plt\.show\(\)', content)
        if show_calls:
            files_with_show.append((file_path, len(show_calls)))
            print(f"  📄 {file_path}: {len(show_calls)} plt.show() calls")
    
    return files_with_show

def create_headless_matplotlib_config():
    """创建无头matplotlib配置"""
    config_content = '''"""
Headless Matplotlib Configuration
无头环境matplotlib配置，避免GUI相关问题
"""

import matplotlib
import os

def setup_headless_matplotlib():
    """设置matplotlib为无头模式"""
    # 检查是否在无头环境中
    if os.environ.get('DISPLAY') is None and os.name != 'nt':
        # Linux/Unix无头环境
        matplotlib.use('Agg')
        print("Matplotlib set to headless mode (Agg backend)")
    elif os.environ.get('MPLBACKEND') == 'Agg':
        # 强制无头模式
        matplotlib.use('Agg')
        print("Matplotlib set to headless mode (forced)")
    else:
        # 正常GUI环境
        print("Matplotlib using default backend")

def disable_plt_show():
    """禁用plt.show()以避免挂起"""
    import matplotlib.pyplot as plt
    
    # 保存原始show函数
    original_show = plt.show
    
    def silent_show(*args, **kwargs):
        """静默的show函数，不显示GUI"""
        print("plt.show() called but suppressed (headless mode)")
        pass
    
    # 替换show函数
    plt.show = silent_show
    
    return original_show

# 自动设置
setup_headless_matplotlib()
'''
    
    config_file = PROJECT_ROOT / "src" / "utils" / "headless_config.py"
    with open(config_file, 'w', encoding='utf-8') as f:
        f.write(config_content)
    
    print(f"✅ Created headless matplotlib config: {config_file}")
    return config_file

def create_safe_visualization_runner():
    """创建安全的可视化运行器"""
    runner_content = '''"""
Safe Visualization Runner
安全的可视化运行器，避免plt.show()挂起问题
"""

import os
import sys
from pathlib import Path
import matplotlib
import matplotlib.pyplot as plt

# Project setup
PROJECT_ROOT = Path(__file__).parent.parent.absolute()
sys.path.insert(0, str(PROJECT_ROOT))

# Force headless mode
matplotlib.use('Agg')

# Disable plt.show() to prevent hanging
def safe_show(*args, **kwargs):
    """Safe show function that doesn't hang"""
    print("plt.show() suppressed (safe mode)")
    pass

plt.show = safe_show

def run_safe_visualization(module_name, function_name):
    """安全运行可视化函数"""
    print(f"Running {module_name}.{function_name} in safe mode...")
    
    try:
        # Import and run the function
        if module_name == "classification":
            from src.data_mining.classification import run_node_classification
            result = run_node_classification()
        elif module_name == "association_rules":
            from src.data_mining.association_rules import run_frequent_subgraph_mining
            result = run_frequent_subgraph_mining()
        elif module_name == "clustering_author":
            from src.data_mining.clustering import run_author_clustering
            result = run_author_clustering()
        elif module_name == "clustering_topic":
            from src.data_mining.clustering import run_paper_topic_clustering
            result = run_paper_topic_clustering()
        elif module_name == "font_test":
            from src.utils.font_config import test_chinese_display
            result = test_chinese_display()
        else:
            print(f"Unknown module: {module_name}")
            return False
        
        if result:
            print(f"✅ {module_name} completed successfully")
            return True
        else:
            print(f"❌ {module_name} failed")
            return False
            
    except Exception as e:
        print(f"❌ Error in {module_name}: {e}")
        return False

def main():
    """主函数"""
    print("Safe Visualization Runner")
    print("=" * 50)
    
    modules_to_run = [
        ("classification", "run_node_classification"),
        ("association_rules", "run_frequent_subgraph_mining"), 
        ("clustering_author", "run_author_clustering"),
        ("clustering_topic", "run_paper_topic_clustering"),
        ("font_test", "test_chinese_display")
    ]
    
    results = {}
    
    for module, function in modules_to_run:
        results[module] = run_safe_visualization(module, function)
    
    # Summary
    print("\\n" + "=" * 50)
    print("SAFE VISUALIZATION RESULTS")
    print("=" * 50)
    
    successful = sum(1 for success in results.values() if success)
    total = len(results)
    
    for module, success in results.items():
        status = "✅" if success else "❌"
        print(f"{status} {module}")
    
    print(f"\\nSuccess Rate: {successful}/{total}")

if __name__ == "__main__":
    main()
'''
    
    runner_file = PROJECT_ROOT / "safe_visualization_runner.py"
    with open(runner_file, 'w', encoding='utf-8') as f:
        f.write(runner_content)
    
    print(f"✅ Created safe visualization runner: {runner_file}")
    return runner_file

def main():
    """主函数"""
    print("Matplotlib plt.show() Issue Fixer")
    print("=" * 50)
    
    # Find files with plt.show()
    files_with_show = find_plt_show_calls()
    
    if files_with_show:
        print(f"\\nFound {len(files_with_show)} files with plt.show() calls")
        print("These can cause hanging in headless environments.")
    else:
        print("\\nNo plt.show() calls found")
    
    # Create solutions
    print("\\nCreating solutions...")
    
    config_file = create_headless_matplotlib_config()
    runner_file = create_safe_visualization_runner()
    
    print("\\n" + "=" * 50)
    print("SOLUTIONS CREATED")
    print("=" * 50)
    
    print("1. Headless Configuration:")
    print(f"   {config_file}")
    print("   Import this in visualization modules to avoid GUI issues")
    
    print("\\n2. Safe Visualization Runner:")
    print(f"   {runner_file}")
    print("   Use this to run visualizations safely without hanging")
    
    print("\\nUsage:")
    print("   python safe_visualization_runner.py")
    
    print("\\nAlternative: Set environment variable before running:")
    print("   export MPLBACKEND=Agg  # Linux/Mac")
    print("   set MPLBACKEND=Agg     # Windows")

if __name__ == "__main__":
    main()
