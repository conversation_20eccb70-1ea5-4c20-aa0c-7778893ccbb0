"""
Enhanced Association Rules Module - Advanced Frequent Subgraph Mining
使用gSpan算法进行真正的频繁子图挖掘，发现复杂的图模式和关联规则
"""

import json
import os
import sys
from pathlib import Path
import networkx as nx
from collections import defaultdict, Counter
import matplotlib.pyplot as plt
import numpy as np
from itertools import combinations, permutations
import pandas as pd

# Project path setup
PROJECT_ROOT = Path(__file__).parent.parent.parent.absolute()
sys.path.insert(0, str(PROJECT_ROOT))

# Font configuration
try:
    from src.utils.font_config import setup_chinese_font
    setup_chinese_font()
except ImportError:
    print("Warning: Font configuration module not available")

# gSpan mining
try:
    from gspan_mining import gSpan
    from gspan_mining.config import parser
    from gspan_mining.graph import Graph
    # Try different import paths for Edge
    try:
        from gspan_mining.edge import Edge
    except ImportError:
        try:
            from gspan_mining.graph import Edge
        except ImportError:
            print("Warning: Edge class not found, will create minimal implementation")
            Edge = None
    GSPAN_AVAILABLE = True
    print("gSpan mining library loaded successfully!")
except ImportError:
    print("Warning: gspan-mining library not properly configured, using fallback methods")
    GSPAN_AVAILABLE = False


class EnhancedFrequentSubgraphMiner:
    """Enhanced frequent subgraph miner using gSpan algorithm"""
    
    def __init__(self, data_file: str = None):
        self.data_file = data_file or str(PROJECT_ROOT / "data" / "processed" / "extracted_entities_relations.json")
        self.main_graph = nx.Graph()
        self.entities = {}
        self.relations = []
        self.graph_database = []
        self.frequent_subgraphs = []
        self.association_rules = []
        self.gspan_results = []
        
    def load_data(self):
        """Load processed data"""
        print("Loading data...")
        
        if not os.path.exists(self.data_file):
            print(f"Data file not found: {self.data_file}")
            return False
        
        with open(self.data_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        self.entities = data.get("entities", {})
        self.relations = data.get("relations", [])
        
        print(f"Loaded: {len(self.relations)} relations")
        return True
        
    def build_main_graph(self):
        """Build main heterogeneous graph"""
        print("Building main graph...")
        
        # Add all nodes with types
        all_nodes = []
        for entity_type, entities in self.entities.items():
            for entity in entities:
                all_nodes.append(entity)
                self.main_graph.add_node(entity, type=entity_type)
        
        # Add edges with relation types
        edge_count = 0
        for relation in self.relations:
            source = relation["source"]
            target = relation["target"]
            
            if source in all_nodes and target in all_nodes:
                self.main_graph.add_edge(source, target, 
                                       relation_type=relation["type"])
                edge_count += 1
        
        print(f"Main graph built: {len(all_nodes)} nodes, {edge_count} edges")
        return len(all_nodes) > 0
        
    def create_graph_database(self, method='ego_networks', radius=2, min_size=3):
        """Create graph database for gSpan mining"""
        print(f"Creating graph database using {method}...")
        
        self.graph_database = []
        
        if method == 'ego_networks':
            # Extract ego networks
            for center_node in self.main_graph.nodes():
                ego_nodes = set([center_node])
                
                # Expand to specified radius
                current_layer = {center_node}
                for _ in range(radius):
                    next_layer = set()
                    for node in current_layer:
                        next_layer.update(self.main_graph.neighbors(node))
                    ego_nodes.update(next_layer)
                    current_layer = next_layer - ego_nodes
                    ego_nodes.update(current_layer)
                
                # Create subgraph
                if len(ego_nodes) >= min_size:
                    ego_subgraph = self.main_graph.subgraph(ego_nodes).copy()
                    self.graph_database.append({
                        'id': len(self.graph_database),
                        'center': center_node,
                        'subgraph': ego_subgraph,
                        'nodes': list(ego_nodes)
                    })
        
        elif method == 'random_walks':
            # Generate subgraphs using random walks
            for start_node in self.main_graph.nodes():
                for walk_length in [4, 6, 8]:
                    walk_nodes = self._random_walk(start_node, walk_length)
                    if len(walk_nodes) >= min_size:
                        walk_subgraph = self.main_graph.subgraph(walk_nodes).copy()
                        self.graph_database.append({
                            'id': len(self.graph_database),
                            'method': 'random_walk',
                            'subgraph': walk_subgraph,
                            'nodes': walk_nodes
                        })
        
        elif method == 'community_detection':
            # Use community detection to create subgraphs
            try:
                import community as community_louvain
                communities = community_louvain.best_partition(self.main_graph)
                
                community_graphs = defaultdict(list)
                for node, comm_id in communities.items():
                    community_graphs[comm_id].append(node)
                
                for comm_id, nodes in community_graphs.items():
                    if len(nodes) >= min_size:
                        comm_subgraph = self.main_graph.subgraph(nodes).copy()
                        self.graph_database.append({
                            'id': len(self.graph_database),
                            'community': comm_id,
                            'subgraph': comm_subgraph,
                            'nodes': nodes
                        })
            except ImportError:
                print("Community detection library not available, falling back to ego networks")
                return self.create_graph_database('ego_networks', radius, min_size)
        
        print(f"Graph database created: {len(self.graph_database)} subgraphs")
        return len(self.graph_database) > 0
        
    def _random_walk(self, start_node, length):
        """Perform random walk from start node"""
        walk = [start_node]
        current_node = start_node
        
        for _ in range(length - 1):
            neighbors = list(self.main_graph.neighbors(current_node))
            if not neighbors:
                break
            current_node = np.random.choice(neighbors)
            if current_node not in walk:  # Avoid revisiting nodes
                walk.append(current_node)
        
        return walk
        
    def convert_to_gspan_format(self):
        """Convert graph database to gSpan format"""
        print("Converting to gSpan format...")
        
        # Create node type and edge type mappings
        node_types = set()
        edge_types = set()
        
        for graph_data in self.graph_database:
            subgraph = graph_data['subgraph']
            for node in subgraph.nodes():
                node_types.add(subgraph.nodes[node].get('type', 'Unknown'))
            for edge in subgraph.edges():
                edge_types.add(subgraph.edges[edge].get('relation_type', 'Unknown'))
        
        node_type_map = {t: i for i, t in enumerate(sorted(node_types))}
        edge_type_map = {t: i for i, t in enumerate(sorted(edge_types))}
        
        print(f"Node types: {node_type_map}")
        print(f"Edge types: {edge_type_map}")
        
        # Convert each subgraph
        gspan_graphs = []
        
        for graph_data in self.graph_database:
            subgraph = graph_data['subgraph']
            
            # Create gSpan graph
            gspan_graph = Graph(graph_data['id'])
            
            # Add nodes
            node_mapping = {}
            for i, node in enumerate(subgraph.nodes()):
                node_type = subgraph.nodes[node].get('type', 'Unknown')
                node_label = node_type_map[node_type]
                gspan_graph.add_vertex(i, node_label)
                node_mapping[node] = i
            
            # Add edges - ensure nodes exist before adding edges
            for edge in subgraph.edges():
                source, target = edge
                edge_type = subgraph.edges[edge].get('relation_type', 'Unknown')
                edge_label = edge_type_map[edge_type]
                
                source_idx = node_mapping.get(source)
                target_idx = node_mapping.get(target)
                
                # Only add edge if both nodes exist in mapping
                if source_idx is not None and target_idx is not None:
                    try:
                        # Try the correct gspan add_edge method signature
                        gspan_graph.add_edge(source_idx, target_idx, edge_label, edge_label)
                    except (TypeError, KeyError) as e:
                        # Skip problematic edges
                        print(f"Warning: Could not add edge {source_idx}-{target_idx}: {e}")
                        continue
            
            gspan_graphs.append(gspan_graph)
        
        self.gspan_graphs = gspan_graphs
        self.node_type_map = node_type_map
        self.edge_type_map = edge_type_map
        
        print(f"Converted {len(gspan_graphs)} graphs to gSpan format")
        return True
        
    def run_gspan_mining(self, min_support=2, min_num_vertices=2, max_num_vertices=6):
        """Run gSpan frequent subgraph mining"""
        print(f"Running gSpan mining (min_support={min_support})...")
        
        if not GSPAN_AVAILABLE:
            print("gSpan not available, using simplified frequent pattern mining...")
            return self._fallback_pattern_mining(min_support)
        
        if not hasattr(self, 'gspan_graphs'):
            self.convert_to_gspan_format()
        
        try:
            # Initialize gSpan
            gspan = gSpan(
                self.gspan_graphs,
                min_support=min_support,
                min_num_vertices=min_num_vertices,
                max_num_vertices=max_num_vertices,
                max_ngraphs=len(self.gspan_graphs)
            )
            
            # Run mining
            gspan.run()
            self.gspan_results = gspan.frequent_subgraphs
            
            print(f"Found {len(self.gspan_results)} frequent subgraphs")
            
            # Convert results back to readable format
            self.frequent_subgraphs = []
            for i, subgraph in enumerate(self.gspan_results):
                try:
                    readable_subgraph = self._convert_gspan_result_to_readable(subgraph)
                    self.frequent_subgraphs.append({
                        'id': i,
                        'support': len(subgraph.graphs),
                        'graph': readable_subgraph,
                        'raw': subgraph
                    })
                except Exception as e:
                    print(f"Warning: Failed to convert subgraph {i}: {e}")
                    continue
            
            return self.frequent_subgraphs
            
        except Exception as e:
            print(f"gSpan mining failed: {e}")
            print("Falling back to simplified pattern mining...")
            return self._fallback_pattern_mining(min_support)
    
    def _fallback_pattern_mining(self, min_support=2):
        """Fallback pattern mining when gSpan fails"""
        print("Using fallback frequent pattern mining...")
        
        # Extract simple patterns from graph database
        pattern_counts = defaultdict(int)
        
        for graph_data in self.graph_database:
            subgraph = graph_data['subgraph']
            
            # Count node type patterns
            node_types = [subgraph.nodes[node].get('type', 'Unknown') 
                         for node in subgraph.nodes()]
            type_counter = Counter(node_types)
            
            # Single node patterns
            for node_type in type_counter:
                pattern = (node_type,)
                pattern_counts[pattern] += 1
            
            # Edge patterns
            for edge in subgraph.edges():
                source, target = edge
                source_type = subgraph.nodes[source].get('type', 'Unknown')
                target_type = subgraph.nodes[target].get('type', 'Unknown')
                edge_type = subgraph.edges[edge].get('relation_type', 'connects')
                
                pattern = (source_type, edge_type, target_type)
                pattern_counts[pattern] += 1
        
        # Filter frequent patterns
        frequent_patterns = {pattern: count for pattern, count in pattern_counts.items() 
                           if count >= min_support}
        
        # Convert to expected format
        self.frequent_subgraphs = []
        for i, (pattern, support) in enumerate(frequent_patterns.items()):
            if len(pattern) == 1:
                # Node pattern
                readable_graph = {
                    'nodes': [0],
                    'edges': [],
                    'node_types': [pattern[0]],
                    'edge_types': []
                }
            elif len(pattern) == 3:
                # Edge pattern
                readable_graph = {
                    'nodes': [0, 1],
                    'edges': [(0, 1)],
                    'node_types': [pattern[0], pattern[2]],
                    'edge_types': [pattern[1]]
                }
            else:
                continue
            
            self.frequent_subgraphs.append({
                'id': i,
                'support': support,
                'graph': readable_graph,
                'raw': None
            })
        
        print(f"Found {len(self.frequent_subgraphs)} frequent patterns using fallback method")
        return self.frequent_subgraphs
        
    def _convert_gspan_result_to_readable(self, gspan_subgraph):
        """Convert gSpan result to readable format"""
        # Reverse mappings
        reverse_node_map = {v: k for k, v in self.node_type_map.items()}
        reverse_edge_map = {v: k for k, v in self.edge_type_map.items()}
        
        # Build readable graph
        readable_graph = {
            'nodes': [],
            'edges': [],
            'node_types': [],
            'edge_types': []
        }
        
        # Add nodes
        for node_id, node_label in gspan_subgraph.vertices.items():
            node_type = reverse_node_map.get(node_label, f'Unknown_{node_label}')
            readable_graph['nodes'].append(node_id)
            readable_graph['node_types'].append(node_type)
        
        # Add edges
        try:
            for edge in gspan_subgraph.edges:
                source = edge.frm
                target = edge.to
                edge_type = reverse_edge_map.get(edge.elb, f'Unknown_{edge.elb}')
                
                readable_graph['edges'].append((source, target))
                readable_graph['edge_types'].append(edge_type)
        except AttributeError:
            # Fallback if edge attributes are different
            print("Warning: Using fallback edge processing")
            for i, edge in enumerate(gspan_subgraph.edges):
                try:
                    source = getattr(edge, 'source', i)
                    target = getattr(edge, 'target', i+1)
                    edge_label = getattr(edge, 'label', 0)
                    edge_type = reverse_edge_map.get(edge_label, f'Unknown_{edge_label}')
                    
                    readable_graph['edges'].append((source, target))
                    readable_graph['edge_types'].append(edge_type)
                except:
                    # Skip problematic edges
                    continue
        
        return readable_graph
        
    def advanced_pattern_analysis(self):
        """Advanced analysis of discovered patterns"""
        print("Performing advanced pattern analysis...")
        
        if not self.frequent_subgraphs:
            print("No frequent subgraphs found")
            return {}
        
        analysis_results = {
            'pattern_statistics': {},
            'motif_analysis': {},
            'structural_properties': {},
            'semantic_patterns': {}
        }
        
        # Pattern statistics
        supports = [fs['support'] for fs in self.frequent_subgraphs]
        sizes = [len(fs['graph']['nodes']) for fs in self.frequent_subgraphs]
        
        analysis_results['pattern_statistics'] = {
            'total_patterns': len(self.frequent_subgraphs),
            'support_distribution': {
                'min': min(supports),
                'max': max(supports),
                'mean': np.mean(supports),
                'std': np.std(supports)
            },
            'size_distribution': {
                'min': min(sizes),
                'max': max(sizes),
                'mean': np.mean(sizes),
                'std': np.std(sizes)
            }
        }
        
        # Motif analysis - find common structural patterns
        motif_counts = defaultdict(int)
        for fs in self.frequent_subgraphs:
            graph = fs['graph']
            num_nodes = len(graph['nodes'])
            num_edges = len(graph['edges'])
            
            if num_nodes == 2 and num_edges == 1:
                motif_counts['dyad'] += 1
            elif num_nodes == 3 and num_edges == 2:
                motif_counts['path_3'] += 1
            elif num_nodes == 3 and num_edges == 3:
                motif_counts['triangle'] += 1
            elif num_nodes == 4 and num_edges == 3:
                motif_counts['star_4'] += 1
            elif num_nodes == 4 and num_edges == 4:
                motif_counts['cycle_4'] += 1
            else:
                motif_counts[f'complex_{num_nodes}_{num_edges}'] += 1
        
        analysis_results['motif_analysis'] = dict(motif_counts)
        
        # Semantic pattern analysis
        semantic_patterns = defaultdict(int)
        type_combinations = defaultdict(int)
        
        for fs in self.frequent_subgraphs:
            graph = fs['graph']
            node_types = graph['node_types']
            edge_types = graph['edge_types']
            
            # Count node type combinations
            for combination in combinations(set(node_types), 2):
                type_combinations[tuple(sorted(combination))] += 1
            
            # Count specific semantic patterns
            if 'Paper' in node_types and 'Author' in node_types:
                semantic_patterns['Paper-Author'] += 1
            if 'Author' in node_types and 'Topic' in node_types:
                semantic_patterns['Author-Topic'] += 1
            if 'Paper' in node_types and 'Subject' in node_types:
                semantic_patterns['Paper-Subject'] += 1
        
        analysis_results['semantic_patterns'] = dict(semantic_patterns)
        analysis_results['type_combinations'] = dict(type_combinations)
        
        return analysis_results
        
    def generate_advanced_association_rules(self, min_confidence=0.6, min_lift=1.2):
        """Generate advanced association rules from frequent subgraphs"""
        print(f"Generating advanced association rules (conf≥{min_confidence}, lift≥{min_lift})...")
        
        self.association_rules = []
        
        # For each frequent subgraph, generate rules
        for fs in self.frequent_subgraphs:
            graph = fs['graph']
            support = fs['support']
            
            node_types = graph['node_types']
            edge_types = graph['edge_types']
            
            # Generate node type rules
            if len(set(node_types)) >= 2:
                for antecedent_size in range(1, len(set(node_types))):
                    for antecedent in combinations(set(node_types), antecedent_size):
                        consequent = tuple(t for t in set(node_types) if t not in antecedent)
                        
                        if consequent:
                            # Calculate confidence and lift
                            confidence, lift = self._calculate_rule_metrics(
                                antecedent, consequent, support
                            )
                            
                            if confidence >= min_confidence and lift >= min_lift:
                                rule = {
                                    'antecedent': antecedent,
                                    'consequent': consequent,
                                    'support': support,
                                    'confidence': confidence,
                                    'lift': lift,
                                    'rule_type': 'node_type',
                                    'source_pattern': fs['id']
                                }
                                self.association_rules.append(rule)
            
            # Generate edge-based rules
            if len(edge_types) >= 1 and len(set(node_types)) >= 2:
                for edge_type in set(edge_types):
                    for node_type in set(node_types):
                        antecedent = (edge_type,)
                        consequent = (node_type,)
                        
                        confidence, lift = self._calculate_edge_node_rule_metrics(
                            edge_type, node_type, support
                        )
                        
                        if confidence >= min_confidence and lift >= min_lift:
                            rule = {
                                'antecedent': antecedent,
                                'consequent': consequent,
                                'support': support,
                                'confidence': confidence,
                                'lift': lift,
                                'rule_type': 'edge_to_node',
                                'source_pattern': fs['id']
                            }
                            self.association_rules.append(rule)
        
        # Sort by confidence * lift
        self.association_rules.sort(key=lambda x: x['confidence'] * x['lift'], reverse=True)
        
        print(f"Generated {len(self.association_rules)} association rules")
        return self.association_rules
        
    def _calculate_rule_metrics(self, antecedent, consequent, support):
        """Calculate confidence and lift for node type rules"""
        # Count antecedent occurrences
        antecedent_count = 0
        full_pattern_count = support
        total_transactions = len(self.graph_database)
        
        for graph_data in self.graph_database:
            subgraph = graph_data['subgraph']
            node_types = [subgraph.nodes[node].get('type', 'Unknown') 
                         for node in subgraph.nodes()]
            
            # Check if antecedent is present
            if all(nt in node_types for nt in antecedent):
                antecedent_count += 1
        
        # Calculate metrics
        confidence = full_pattern_count / antecedent_count if antecedent_count > 0 else 0
        
        # For lift, estimate consequent probability
        consequent_count = 0
        for graph_data in self.graph_database:
            subgraph = graph_data['subgraph']
            node_types = [subgraph.nodes[node].get('type', 'Unknown') 
                         for node in subgraph.nodes()]
            
            if all(nt in node_types for nt in consequent):
                consequent_count += 1
        
        consequent_prob = consequent_count / total_transactions if total_transactions > 0 else 0
        lift = confidence / consequent_prob if consequent_prob > 0 else 0
        
        return confidence, lift
        
    def _calculate_edge_node_rule_metrics(self, edge_type, node_type, support):
        """Calculate confidence and lift for edge-to-node rules"""
        edge_count = 0
        total_transactions = len(self.graph_database)
        
        for graph_data in self.graph_database:
            subgraph = graph_data['subgraph']
            edge_types = [subgraph.edges[edge].get('relation_type', 'Unknown') 
                         for edge in subgraph.edges()]
            
            if edge_type in edge_types:
                edge_count += 1
        
        confidence = support / edge_count if edge_count > 0 else 0
        
        # Estimate node type probability
        node_count = 0
        for graph_data in self.graph_database:
            subgraph = graph_data['subgraph']
            node_types = [subgraph.nodes[node].get('type', 'Unknown') 
                         for node in subgraph.nodes()]
            
            if node_type in node_types:
                node_count += 1
        
        node_prob = node_count / total_transactions if total_transactions > 0 else 0
        lift = confidence / node_prob if node_prob > 0 else 0
        
        return confidence, lift
        
    def visualize_frequent_subgraphs(self, top_k=10, output_file=None):
        """Visualize top frequent subgraphs"""
        print(f"Visualizing top {top_k} frequent subgraphs...")
        
        if not self.frequent_subgraphs:
            print("No frequent subgraphs to visualize")
            return
        
        # Sort by support
        sorted_subgraphs = sorted(self.frequent_subgraphs, 
                                key=lambda x: x['support'], reverse=True)[:top_k]
        
        # Create subplot grid
        rows = (len(sorted_subgraphs) + 2) // 3
        cols = min(3, len(sorted_subgraphs))
        
        fig, axes = plt.subplots(rows, cols, figsize=(15, 5 * rows))
        if rows == 1:
            axes = [axes] if cols == 1 else axes
        else:
            axes = axes.flatten()
        
        # Color mapping for node types
        type_colors = {
            'Paper': 'red',
            'Author': 'blue',
            'Topic': 'green',
            'Subject': 'orange',
            'Unknown': 'gray'
        }
        
        for i, fs in enumerate(sorted_subgraphs):
            if i >= len(axes):
                break
                
            ax = axes[i]
            graph_data = fs['graph']
            
            # Create NetworkX graph for visualization
            G = nx.Graph()
            
            # Add nodes with types
            for j, node_type in enumerate(graph_data['node_types']):
                G.add_node(j, type=node_type)
            
            # Add edges
            for edge in graph_data['edges']:
                G.add_edge(edge[0], edge[1])
            
            # Draw graph
            pos = nx.spring_layout(G, k=1, iterations=50)
            
            # Draw nodes with colors based on type
            for node_type, color in type_colors.items():
                nodes = [n for n in G.nodes() if G.nodes[n]['type'] == node_type]
                if nodes:
                    nx.draw_networkx_nodes(G, pos, nodelist=nodes, 
                                         node_color=color, node_size=300, 
                                         alpha=0.8, ax=ax)
            
            # Draw edges
            nx.draw_networkx_edges(G, pos, alpha=0.6, ax=ax)
            
            # Draw labels
            nx.draw_networkx_labels(G, pos, font_size=8, ax=ax)
            
            ax.set_title(f'Pattern {fs["id"]}\nSupport: {fs["support"]}')
            ax.axis('off')
        
        # Remove empty subplots
        for i in range(len(sorted_subgraphs), len(axes)):
            axes[i].remove()
        
        plt.tight_layout()
        
        # Save visualization
        if not output_file:
            output_file = str(PROJECT_ROOT / "data" / "graphs" / "frequent_subgraphs_gspan.png")
        
        os.makedirs(os.path.dirname(output_file), exist_ok=True)
        plt.savefig(output_file, dpi=300, bbox_inches='tight')
        print(f"Frequent subgraphs visualization saved to: {output_file}")
        
        plt.show()
        
    def create_comprehensive_report(self):
        """Create comprehensive mining report"""
        print("Creating comprehensive mining report...")
        
        if not self.frequent_subgraphs:
            print("No patterns found to report")
            return None
        
        # Perform analysis
        analysis = self.advanced_pattern_analysis()
        
        report = {
            'mining_summary': {
                'total_frequent_subgraphs': len(self.frequent_subgraphs),
                'total_association_rules': len(self.association_rules),
                'graph_database_size': len(self.graph_database),
                'main_graph_nodes': len(self.main_graph.nodes()),
                'main_graph_edges': len(self.main_graph.edges())
            },
            'pattern_analysis': analysis,
            'top_patterns': [],
            'top_rules': [],
            'insights': []
        }
        
        # Top patterns
        sorted_patterns = sorted(self.frequent_subgraphs, 
                               key=lambda x: x['support'], reverse=True)[:10]
        
        for pattern in sorted_patterns:
            report['top_patterns'].append({
                'id': pattern['id'],
                'support': pattern['support'],
                'nodes': len(pattern['graph']['nodes']),
                'edges': len(pattern['graph']['edges']),
                'node_types': pattern['graph']['node_types'],
                'edge_types': pattern['graph']['edge_types']
            })
        
        # Top rules
        top_rules = sorted(self.association_rules, 
                         key=lambda x: x['confidence'] * x['lift'], reverse=True)[:10]
        
        for rule in top_rules:
            report['top_rules'].append({
                'antecedent': rule['antecedent'],
                'consequent': rule['consequent'],
                'confidence': rule['confidence'],
                'lift': rule['lift'],
                'support': rule['support'],
                'type': rule['rule_type']
            })
        
        # Generate insights
        report['insights'] = self._generate_insights(analysis)
        
        return report
        
    def _generate_insights(self, analysis):
        """Generate actionable insights from analysis"""
        insights = []
        
        # Pattern insights
        if analysis['motif_analysis']:
            most_common_motif = max(analysis['motif_analysis'].items(), key=lambda x: x[1])
            insights.append(f"Most common structural pattern: {most_common_motif[0]} (found {most_common_motif[1]} times)")
        
        # Semantic insights
        if analysis['semantic_patterns']:
            top_semantic = max(analysis['semantic_patterns'].items(), key=lambda x: x[1])
            insights.append(f"Strongest semantic association: {top_semantic[0]} (appears in {top_semantic[1]} patterns)")
        
        # Rule insights
        if self.association_rules:
            high_conf_rules = [r for r in self.association_rules if r['confidence'] > 0.8]
            insights.append(f"Found {len(high_conf_rules)} high-confidence rules (>80%)")
            
            if high_conf_rules:
                best_rule = max(high_conf_rules, key=lambda x: x['lift'])
                insights.append(f"Best rule: {' + '.join(best_rule['antecedent'])} => {' + '.join(best_rule['consequent'])} (conf: {best_rule['confidence']:.3f}, lift: {best_rule['lift']:.3f})")
        
        return insights
        
    def save_enhanced_results(self, output_file=None):
        """Save all mining results"""
        if not output_file:
            output_file = str(PROJECT_ROOT / "data" / "processed" / "enhanced_gspan_mining_results.json")
        
        # Create comprehensive report
        report = self.create_comprehensive_report()
        
        # Convert tuple keys to strings for JSON serialization
        def convert_tuple_keys(obj):
            if isinstance(obj, dict):
                new_dict = {}
                for k, v in obj.items():
                    if isinstance(k, tuple):
                        new_key = str(k)
                    else:
                        new_key = k
                    new_dict[new_key] = convert_tuple_keys(v)
                return new_dict
            elif isinstance(obj, list):
                return [convert_tuple_keys(item) for item in obj]
            else:
                return obj
        
        # Convert the report
        serializable_report = convert_tuple_keys(report)
        
        save_data = {
            "mining_method": "Enhanced gSpan Frequent Subgraph Mining",
            "algorithm": "gSpan with Advanced Pattern Analysis",
            "parameters": {
                "min_support": getattr(self, 'min_support', 2),
                "graph_database_method": "ego_networks",
                "radius": 2
            },
            "comprehensive_report": serializable_report,
            "generated_at": __import__('datetime').datetime.now().isoformat()
        }
        
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(save_data, f, ensure_ascii=False, indent=2)
        
        print(f"Enhanced mining results saved to: {output_file}")
        
        # Also save patterns in CSV format for easy analysis
        csv_file = output_file.replace('.json', '_patterns.csv')
        if self.frequent_subgraphs:
            patterns_df = pd.DataFrame([
                {
                    'pattern_id': fs['id'],
                    'support': fs['support'],
                    'num_nodes': len(fs['graph']['nodes']),
                    'num_edges': len(fs['graph']['edges']),
                    'node_types': ', '.join(fs['graph']['node_types']),
                    'edge_types': ', '.join(fs['graph']['edge_types'])
                } for fs in self.frequent_subgraphs
            ])
            patterns_df.to_csv(csv_file, index=False)
            print(f"Pattern summary saved to: {csv_file}")
        
        return output_file


def run_enhanced_gspan_mining():
    """Run enhanced gSpan frequent subgraph mining"""
    print("=== Enhanced gSpan Frequent Subgraph Mining ===")
    
    miner = EnhancedFrequentSubgraphMiner()
    
    # Load data
    if not miner.load_data():
        return None
    
    # Build main graph
    if not miner.build_main_graph():
        print("Failed to build main graph")
        return None
    
    # Create graph database
    if not miner.create_graph_database(method='ego_networks', radius=2):
        print("Failed to create graph database")
        return None
    
    # Convert to gSpan format
    if not miner.convert_to_gspan_format():
        print("Failed to convert to gSpan format")
        return None
    
    # Run gSpan mining
    print("\n--- Running gSpan Algorithm ---")
    frequent_subgraphs = miner.run_gspan_mining(min_support=2, max_num_vertices=5)
    
    if not frequent_subgraphs:
        print("No frequent subgraphs found")
        return miner
    
    # Advanced pattern analysis
    print("\n--- Advanced Pattern Analysis ---")
    analysis = miner.advanced_pattern_analysis()
    
    # Generate association rules
    print("\n--- Generating Association Rules ---")
    rules = miner.generate_advanced_association_rules(min_confidence=0.6, min_lift=1.1)
    
    # Visualize results
    try:
        miner.visualize_frequent_subgraphs(top_k=9)
    except Exception as e:
        print(f"Visualization failed: {e}")
    
    # Save results
    output_file = miner.save_enhanced_results()
    
    # Print summary
    report = miner.create_comprehensive_report()
    print("\n=== Enhanced gSpan Mining Complete ===")
    print(f"Frequent subgraphs found: {len(frequent_subgraphs)}")
    print(f"Association rules generated: {len(rules)}")
    print(f"Results saved to: {output_file}")
    
    if report and report['insights']:
        print("\nKey Insights:")
        for insight in report['insights']:
            print(f"  [+] {insight}")
    
    return miner


if __name__ == "__main__":
    miner = run_enhanced_gspan_mining()
