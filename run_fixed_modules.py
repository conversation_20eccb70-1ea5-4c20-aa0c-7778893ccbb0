"""
Run Fixed Data Mining Modules
运行修复后的数据挖掘模块，生成科学有效的结果
"""

import os
import sys
from pathlib import Path
import time

# Project setup
PROJECT_ROOT = Path(__file__).parent.absolute()
sys.path.insert(0, str(PROJECT_ROOT))

def print_header():
    """打印标题"""
    print("FIXED DATA MINING MODULES RUNNER")
    print("=" * 60)
    print("Running scientifically corrected versions of:")
    print("  • Classification (no data leakage)")
    print("  • Association Rules (valid confidence <= 1.0)")
    print("=" * 60)

def check_data_availability():
    """检查数据文件"""
    data_file = PROJECT_ROOT / "data" / "processed" / "extracted_entities_relations.json"
    if not data_file.exists():
        print(f"❌ Data file not found: {data_file}")
        print("Please run data extraction first:")
        print("  python src/knowledge_construction/extractor.py")
        return False
    
    print(f"✅ Data file found: {data_file}")
    return True

def run_fixed_classification():
    """运行修复后的分类模块"""
    print("\n🔧 RUNNING FIXED CLASSIFICATION MODULE")
    print("-" * 50)
    print("Fixes applied:")
    print("  • Proper train/test split BEFORE feature engineering")
    print("  • Centrality computed on training subgraph only")
    print("  • Test nodes use local features only")
    print()
    
    try:
        from src.data_mining.classification_enhanced_fixed import run_fixed_classification
        
        start_time = time.time()
        classifier = run_fixed_classification()
        end_time = time.time()
        
        if classifier:
            print(f"\n✅ Fixed classification completed in {end_time - start_time:.2f} seconds")
            return True
        else:
            print("❌ Fixed classification failed")
            return False
            
    except Exception as e:
        print(f"❌ Error running fixed classification: {e}")
        return False

def run_fixed_association_rules():
    """运行修复后的关联规则模块"""
    print("\n🔧 RUNNING FIXED ASSOCIATION RULES MODULE")
    print("-" * 50)
    print("Fixes applied:")
    print("  • Corrected antecedent support counting")
    print("  • Confidence validation (≤ 1.0)")
    print("  • Mathematical assertions added")
    print()
    
    try:
        from src.data_mining.association_rules_enhanced_fixed import run_fixed_gspan_mining
        
        start_time = time.time()
        miner = run_fixed_gspan_mining()
        end_time = time.time()
        
        if miner:
            print(f"\n✅ Fixed association rules completed in {end_time - start_time:.2f} seconds")
            return True
        else:
            print("❌ Fixed association rules failed")
            return False
            
    except Exception as e:
        print(f"❌ Error running fixed association rules: {e}")
        return False

def validate_results():
    """验证结果"""
    print("\n🔍 VALIDATING FIXED RESULTS")
    print("-" * 50)
    
    try:
        # Import and run validation
        exec(open(PROJECT_ROOT / "quick_validation.py").read())
        return True
    except Exception as e:
        print(f"❌ Validation failed: {e}")
        return False

def check_output_files():
    """检查输出文件"""
    print("\n📁 CHECKING OUTPUT FILES")
    print("-" * 50)
    
    expected_files = [
        "data/processed/fixed_classification_results.json",
        "data/processed/fixed_association_rules_results.json"
    ]
    
    all_exist = True
    for file_path in expected_files:
        full_path = PROJECT_ROOT / file_path
        if full_path.exists():
            file_size = full_path.stat().st_size / 1024  # KB
            print(f"✅ {file_path} ({file_size:.1f} KB)")
        else:
            print(f"❌ {file_path} (missing)")
            all_exist = False
    
    return all_exist

def print_usage_instructions():
    """打印使用说明"""
    print("\n📋 USAGE INSTRUCTIONS")
    print("-" * 50)
    print("Fixed modules are now available:")
    print()
    print("🔧 Individual module execution:")
    print("  python src/data_mining/classification_enhanced_fixed.py")
    print("  python src/data_mining/association_rules_enhanced_fixed.py")
    print()
    print("🔍 Validation:")
    print("  python quick_validation.py")
    print()
    print("📊 Results location:")
    print("  data/processed/fixed_classification_results.json")
    print("  data/processed/fixed_association_rules_results.json")
    print()
    print("📖 Documentation:")
    print("  FIXES_SUMMARY.md - Complete fix documentation")

def main():
    """主函数"""
    print_header()
    
    # Check data availability
    if not check_data_availability():
        return
    
    # Track results
    results = {}
    
    # Run fixed modules
    print("\n🚀 EXECUTING FIXED MODULES")
    print("=" * 60)
    
    results['classification'] = run_fixed_classification()
    results['association_rules'] = run_fixed_association_rules()
    
    # Check output files
    files_ok = check_output_files()
    
    # Validate results
    if all(results.values()) and files_ok:
        validation_ok = validate_results()
    else:
        validation_ok = False
    
    # Summary
    print("\n" + "=" * 60)
    print("🎯 EXECUTION SUMMARY")
    print("=" * 60)
    
    successful_modules = sum(1 for success in results.values() if success)
    total_modules = len(results)
    
    print(f"Module Success Rate: {successful_modules}/{total_modules}")
    
    for module, success in results.items():
        status = "✅" if success else "❌"
        print(f"  {status} {module}")
    
    print(f"Output Files: {'✅' if files_ok else '❌'}")
    print(f"Validation: {'✅' if validation_ok else '❌'}")
    
    if successful_modules == total_modules and files_ok and validation_ok:
        print("\n🎉 ALL FIXED MODULES EXECUTED SUCCESSFULLY!")
        print("✅ Data leakage eliminated")
        print("✅ Confidence values mathematically valid")
        print("✅ Results ready for scientific publication")
        
        print_usage_instructions()
        
    else:
        print("\n⚠️  SOME ISSUES OCCURRED")
        print("Please check the error messages above")
    
    print(f"\n📄 For detailed fix documentation, see: FIXES_SUMMARY.md")

if __name__ == "__main__":
    main()
