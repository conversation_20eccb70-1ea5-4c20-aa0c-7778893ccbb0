"""
Visualization Status Checker
检查和报告所有可视化文件的状态，确保没有遗漏
"""

import os
from pathlib import Path
import json
from datetime import datetime

# Project setup
PROJECT_ROOT = Path(__file__).parent.absolute()

def check_visualization_files():
    """检查所有可视化文件的状态"""
    print("Knowledge Graph Mining - Visualization Status Report")
    print("=" * 60)
    
    graphs_dir = PROJECT_ROOT / "data" / "graphs"
    
    # Expected visualizations based on codebase analysis
    expected_visualizations = {
        "enhanced_classification_results.png": {
            "description": "Enhanced multi-model classification comparison",
            "source_module": "src/data_mining/classification_enhanced.py",
            "function": "visualize_model_comparison()",
            "priority": "HIGH"
        },
        "frequent_subgraphs_gspan.png": {
            "description": "gSpan frequent subgraph patterns",
            "source_module": "src/data_mining/association_rules_enhanced.py", 
            "function": "visualize_frequent_subgraphs()",
            "priority": "HIGH"
        },
        "classification_result.png": {
            "description": "Original classification results visualization",
            "source_module": "src/data_mining/classification.py",
            "function": "visualize_classification_results()",
            "priority": "MEDIUM"
        },
        "frequent_node_patterns.png": {
            "description": "Frequent node pattern analysis",
            "source_module": "src/data_mining/association_rules.py",
            "function": "visualize_frequent_patterns(patterns, 'node')",
            "priority": "MEDIUM"
        },
        "frequent_edge_patterns.png": {
            "description": "Frequent edge pattern analysis", 
            "source_module": "src/data_mining/association_rules.py",
            "function": "visualize_frequent_patterns(patterns, 'edge')",
            "priority": "MEDIUM"
        },
        "author_collaboration_clusters.png": {
            "description": "Author collaboration network clustering",
            "source_module": "src/data_mining/clustering.py",
            "function": "run_author_clustering() -> visualize_clusters()",
            "priority": "MEDIUM"
        },
        "paper_topic_clusters.png": {
            "description": "Paper-topic network clustering",
            "source_module": "src/data_mining/clustering.py", 
            "function": "run_paper_topic_clustering() -> visualize_clusters()",
            "priority": "MEDIUM"
        },
        "font_test.png": {
            "description": "Chinese font configuration test",
            "source_module": "src/utils/font_config.py",
            "function": "test_chinese_display()",
            "priority": "LOW"
        }
    }
    
    # Check which files exist
    existing_files = []
    missing_files = []
    
    if graphs_dir.exists():
        existing_files = list(graphs_dir.glob("*.png"))
        
    print(f"Graphs Directory: {graphs_dir}")
    print(f"Directory Exists: {'✅' if graphs_dir.exists() else '❌'}")
    print()
    
    # Status report
    print("VISUALIZATION STATUS REPORT")
    print("-" * 60)
    
    total_expected = len(expected_visualizations)
    found_count = 0
    
    for filename, info in expected_visualizations.items():
        file_path = graphs_dir / filename
        exists = file_path.exists()
        
        if exists:
            found_count += 1
            file_size = file_path.stat().st_size / 1024  # KB
            status = f"✅ EXISTS ({file_size:.1f} KB)"
        else:
            status = "❌ MISSING"
        
        priority_icon = {"HIGH": "🔴", "MEDIUM": "🟡", "LOW": "🟢"}[info["priority"]]
        
        print(f"{priority_icon} {filename}")
        print(f"   Status: {status}")
        print(f"   Description: {info['description']}")
        print(f"   Source: {info['source_module']}")
        print(f"   Function: {info['function']}")
        print()
    
    # Summary
    print("SUMMARY")
    print("-" * 60)
    print(f"Expected Visualizations: {total_expected}")
    print(f"Found Visualizations: {found_count}")
    print(f"Missing Visualizations: {total_expected - found_count}")
    print(f"Completion Rate: {(found_count/total_expected)*100:.1f}%")
    
    # Additional files (not in expected list)
    if existing_files:
        expected_names = set(expected_visualizations.keys())
        additional_files = [f for f in existing_files if f.name not in expected_names]
        
        if additional_files:
            print(f"\nADDITIONAL FILES (not in expected list):")
            for file in additional_files:
                file_size = file.stat().st_size / 1024
                print(f"  📄 {file.name} ({file_size:.1f} KB)")
    
    return found_count, total_expected, expected_visualizations

def generate_execution_commands():
    """生成执行命令来生成缺失的可视化"""
    print("\n" + "=" * 60)
    print("EXECUTION COMMANDS TO GENERATE MISSING VISUALIZATIONS")
    print("=" * 60)
    
    commands = {
        "All Enhanced Visualizations": "python auto_run_enhanced.py",
        "All Visualizations (Comprehensive)": "python run_all_visualizations.py", 
        "Missing Visualizations Only": "python generate_missing_visualizations.py",
        "Original Classification": "python src/data_mining/classification.py",
        "Original Association Rules": "python src/data_mining/association_rules.py",
        "Clustering Analysis": "python src/data_mining/clustering.py",
        "Font Configuration Test": "python src/utils/font_config.py"
    }
    
    for description, command in commands.items():
        print(f"📋 {description}:")
        print(f"   {command}")
        print()

def check_data_dependencies():
    """检查数据依赖是否满足"""
    print("DATA DEPENDENCIES CHECK")
    print("-" * 60)
    
    required_files = [
        "data/processed/extracted_entities_relations.json",
        "data/processed/enhanced_classification_results.json", 
        "data/processed/enhanced_gspan_mining_results.json"
    ]
    
    all_good = True
    
    for file_path in required_files:
        full_path = PROJECT_ROOT / file_path
        exists = full_path.exists()
        
        if exists:
            file_size = full_path.stat().st_size / 1024
            status = f"✅ EXISTS ({file_size:.1f} KB)"
        else:
            status = "❌ MISSING"
            all_good = False
        
        print(f"{status} {file_path}")
    
    print()
    if all_good:
        print("✅ All required data files are available")
    else:
        print("❌ Some required data files are missing")
        print("   Please run data extraction and processing first:")
        print("   1. python src/knowledge_construction/extractor.py")
        print("   2. python src/knowledge_construction/loader.py") 
        print("   3. python auto_run_enhanced.py")
    
    return all_good

def save_status_report():
    """保存状态报告到JSON文件"""
    found_count, total_expected, expected_viz = check_visualization_files()
    
    report = {
        "timestamp": datetime.now().isoformat(),
        "summary": {
            "total_expected": total_expected,
            "found_count": found_count,
            "missing_count": total_expected - found_count,
            "completion_rate": (found_count/total_expected)*100
        },
        "visualizations": {}
    }
    
    graphs_dir = PROJECT_ROOT / "data" / "graphs"
    
    for filename, info in expected_viz.items():
        file_path = graphs_dir / filename
        exists = file_path.exists()
        
        report["visualizations"][filename] = {
            "exists": exists,
            "file_size_kb": file_path.stat().st_size / 1024 if exists else 0,
            "description": info["description"],
            "source_module": info["source_module"],
            "priority": info["priority"]
        }
    
    # Save report
    report_file = PROJECT_ROOT / "visualization_status_report.json"
    with open(report_file, 'w', encoding='utf-8') as f:
        json.dump(report, f, ensure_ascii=False, indent=2)
    
    print(f"\n📊 Status report saved to: {report_file}")
    return report_file

def main():
    """主函数"""
    print("Starting comprehensive visualization status check...\n")
    
    # Check data dependencies first
    data_ok = check_data_dependencies()
    print()
    
    if not data_ok:
        print("⚠️  Data dependencies not met. Please resolve before generating visualizations.\n")
    
    # Check visualization status
    found_count, total_expected, _ = check_visualization_files()
    
    # Generate execution commands
    generate_execution_commands()
    
    # Save detailed report
    save_status_report()
    
    # Final recommendation
    print("RECOMMENDATIONS")
    print("-" * 60)
    
    if found_count == total_expected:
        print("🎉 All visualizations are present! Your project is complete.")
    elif found_count >= 6:
        print("✅ Most visualizations are present. You have a comprehensive set.")
        print("   Consider running missing visualization scripts if you need 100% completion.")
    else:
        print("⚠️  Several visualizations are missing.")
        print("   Recommended action: python run_all_visualizations.py")
    
    print(f"\n📁 All visualization files should be in: {PROJECT_ROOT / 'data' / 'graphs'}")

if __name__ == "__main__":
    main()
