#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import json
import os
import sys
from datetime import datetime
from pathlib import Path

# 添加项目路径
sys.path.append(r'C:\dev\MCP\knowledge-graph-mining')

def process_ml_papers_directly():
    """直接处理机器学习论文JSON数据"""
    
    # 读取我们的数据
    crawl_data_path = r"C:\dev\MCP\knowledge-graph-mining\data\raw\machine_learning_papers_crawl.json"
    
    if not os.path.exists(crawl_data_path):
        print(f"错误: 找不到文件 {crawl_data_path}")
        return
    
    with open(crawl_data_path, 'r', encoding='utf-8') as f:
        crawl_data = json.load(f)
    
    # 直接处理JSON数据
    research_query = "Recent machine learning advances and applications"
    papers = crawl_data.get("selected_papers", [])
    
    print(f"开始处理机器学习论文数据...")
    print(f"原始论文数量: {len(papers)}")
    
    # 转换为标准格式
    processed_papers = []
    entities_relations = {
        "papers": [],
        "authors": [],
        "topics": [],
        "relations": {
            "author_paper": [],
            "paper_topic": [],
            "author_collaboration": []
        }
    }
    
    for i, paper in enumerate(papers):
        # 处理论文信息
        arxiv_id = paper.get("id", "")
        title = paper.get("title", "")
        authors = paper.get("authors", [])
        subjects = paper.get("subjects", [])
        
        paper_info = {
            "paper_id": str(i + 1),
            "arxiv_id": arxiv_id,
            "title": title,
            "authors": authors,
            "subjects": subjects,
            "relevance_score": 1.0,  # 所有都是相关的
            "research_context": research_query,
            "extraction_timestamp": datetime.now().isoformat()
        }
        processed_papers.append(paper_info)
        
        # 添加论文实体
        paper_entity = {
            "id": f"paper_{arxiv_id}",
            "type": "Paper",
            "title": title,
            "arxiv_id": arxiv_id,
            "relevance_score": 1.0
        }
        entities_relations["papers"].append(paper_entity)
        
        # 处理作者
        for author in authors:
            author_id = f"author_{author.replace(' ', '_').replace('.', '')}"
            
            # 添加作者实体（去重）
            if not any(a["id"] == author_id for a in entities_relations["authors"]):
                author_entity = {
                    "id": author_id,
                    "type": "Author",
                    "name": author
                }
                entities_relations["authors"].append(author_entity)
            
            # 添加作者-论文关系
            relation = {
                "source": author_id,
                "target": paper_entity["id"],
                "type": "AUTHORED"
            }
            entities_relations["relations"]["author_paper"].append(relation)
        
        # 处理主题
        for subject in subjects:
            # 清理主题名称
            subject_clean = subject.replace("(", "").replace(")", "").strip()
            topic_id = f"topic_{subject_clean.replace(' ', '_').replace('.', '')}"
            
            # 添加主题实体（去重）
            if not any(t["id"] == topic_id for t in entities_relations["topics"]):
                topic_entity = {
                    "id": topic_id,
                    "type": "Topic",
                    "name": subject_clean
                }
                entities_relations["topics"].append(topic_entity)
            
            # 添加论文-主题关系
            relation = {
                "source": paper_entity["id"],
                "target": topic_id,
                "type": "BELONGS_TO"
            }
            entities_relations["relations"]["paper_topic"].append(relation)
    
    # 保存结果
    save_results(processed_papers, entities_relations, research_query)
    
    # 生成报告
    generate_report(processed_papers, entities_relations)
    
    return processed_papers, entities_relations

def save_results(papers, entities_relations, research_query):
    """保存处理结果"""
    
    # 确保目录存在
    output_dir = Path(r"C:\dev\MCP\knowledge-graph-mining\data\processed")
    output_dir.mkdir(parents=True, exist_ok=True)
    
    # 保存论文数据
    papers_file = output_dir / "papers.json"
    with open(papers_file, 'w', encoding='utf-8') as f:
        json.dump(papers, f, ensure_ascii=False, indent=2)
    
    # 保存实体数据
    entities_file = output_dir / "entities.json"
    entities_data = {
        "papers": entities_relations["papers"],
        "authors": entities_relations["authors"],
        "topics": entities_relations["topics"]
    }
    with open(entities_file, 'w', encoding='utf-8') as f:
        json.dump(entities_data, f, ensure_ascii=False, indent=2)
    
    # 保存关系数据
    relations_file = output_dir / "relations.json"
    with open(relations_file, 'w', encoding='utf-8') as f:
        json.dump(entities_relations["relations"], f, ensure_ascii=False, indent=2)
    
    # 保存完整结果
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    complete_file = output_dir / f"ml_papers_complete_{timestamp}.json"
    complete_data = {
        "research_query": research_query,
        "processing_timestamp": datetime.now().isoformat(),
        "papers": papers,
        "entities_relations": entities_relations,
        "statistics": {
            "total_papers": len(papers),
            "total_authors": len(entities_relations["authors"]),
            "total_topics": len(entities_relations["topics"]),
            "total_relations": sum(len(relations) for relations in entities_relations["relations"].values())
        }
    }
    with open(complete_file, 'w', encoding='utf-8') as f:
        json.dump(complete_data, f, ensure_ascii=False, indent=2)
    
    print(f"结果已保存到: {output_dir}")
    print(f"完整数据文件: {complete_file}")

def generate_report(papers, entities_relations):
    """生成处理报告"""
    
    print(f"\n{' 机器学习论文处理完成 ':#^60}")
    
    print(f"\n[RESULTS] 处理结果:")
    print(f"   - 论文总数: {len(papers)}")
    print(f"   - 作者总数: {len(entities_relations['authors'])}")
    print(f"   - 主题总数: {len(entities_relations['topics'])}")
    print(f"   - 关系总数: {sum(len(relations) for relations in entities_relations['relations'].values())}")
    
    print(f"\n[AUTHORS] 主要作者:")
    for i, author in enumerate(entities_relations['authors'][:10]):
        print(f"   - {author['name']}")
    
    print(f"\n[TOPICS] 主要研究主题:")
    for topic in entities_relations['topics'][:10]:
        print(f"   - {topic['name']}")
    
    print(f"\n[PAPERS] 部分论文标题:")
    for i, paper in enumerate(papers[:5]):
        print(f"   {i+1}. {paper['title']}")
    
    print(f"\n{'='*60}")

if __name__ == "__main__":
    try:
        papers, entities = process_ml_papers_directly()
        print(f"\n处理完成！共处理 {len(papers)} 篇论文")
    except Exception as e:
        print(f"处理出错: {e}")
        import traceback
        traceback.print_exc()
