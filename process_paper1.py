"""
处理crawl4ai抓取结果的脚本
"""

import sys
import os
from pathlib import Path

# 设置项目路径
PROJECT_ROOT = Path(r"C:\dev\MCP\knowledge-graph-mining")
sys.path.insert(0, str(PROJECT_ROOT))

# 导入我们的模块
from src.data_acquisition.crawler import ArxivCrawler

def process_first_paper():
    """处理第一篇论文的抓取结果"""
    
    # crawl4ai抓取的结果
    arxiv_id = "2301.00001"
    markdown_content = """# Computer Science > Human-Computer Interaction
**arXiv:2301.00001** (cs) 
[Submitted on 21 Dec 2022]
#  Title:NFTrig
Authors: <AUTHORS>
View a PDF of the paper titled NFTrig, by Jordan Thompson and 5 other authors
[View PDF](https://arxiv.org/pdf/2301.00001)
> Abstract:NFTrig is a web-based application created for use as an educational tool to teach trigonometry and block chain technology. Creation of the application includes front and back end development as well as integration with other outside sources including MetaMask and OpenSea. The primary development languages include HTML, CSS (Bootstrap 5), and JavaScript as well as Solidity for smart contract creation. The application itself is hosted on Moralis utilizing their Web3 API. This technical report describes how the application was created, what the application requires, and smart contract design with security considerations in mind. The NFTrig application has underwent significant testing and validation prior to and after deployment. Future suggestions and recommendations for further development, maintenance, and use in other fields for education are also described. 
Subjects: |  Human-Computer Interaction (cs.HC)  
---|---  
Cite as: | [arXiv:2301.00001](https://arxiv.org/abs/2301.00001) [cs.HC]  
(or  [arXiv:2301.00001v1](https://arxiv.org/abs/2301.00001v1) [cs.HC] for this version)   
<https://doi.org/10.48550/arXiv.2301.00001> Focus to learn more  
## Submission history
From: Tauheed Khan Mohd [[view email](https://arxiv.org/show-email/b39d47e2/2301.00001)] **[v1]** Wed, 21 Dec 2022 18:07:06 UTC (1,260 KB)"""
    
    print(f"处理论文: {arxiv_id}")
    
    # 创建爬虫实例
    crawler = ArxivCrawler(output_dir=str(PROJECT_ROOT / "data" / "raw"))
    
    # 提取论文信息
    paper_info = crawler.extract_paper_info(markdown_content, arxiv_id)
    
    # 保存数据
    filepath = crawler.save_paper_data(paper_info)
    
    print(f"论文信息提取完成:")
    print(f"  标题: {paper_info['title']}")
    print(f"  作者: {len(paper_info['authors'])} 人 - {', '.join(paper_info['authors'][:3])}...")
    print(f"  摘要长度: {len(paper_info['abstract'])} 字符")
    print(f"  学科: {len(paper_info['subjects'])} 个")
    print(f"  保存到: {filepath}")
    
    return paper_info

if __name__ == "__main__":
    # 切换到项目目录
    os.chdir(PROJECT_ROOT)
    
    # 处理第一篇论文
    result = process_first_paper()
    
    print("\n=== 数据处理完成 ===")
    print("请继续抓取其他论文，或运行知识图谱构建流程")
