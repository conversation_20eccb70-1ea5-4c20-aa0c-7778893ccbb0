"""
Validate Suspicious Results
验证可疑的结果：100%准确率和>100%置信度
"""

import json
import numpy as np
from pathlib import Path
import sys

# Project setup
PROJECT_ROOT = Path(__file__).parent.absolute()
sys.path.insert(0, str(PROJECT_ROOT))

def load_results():
    """加载结果文件"""
    results = {}
    
    # Enhanced classification results
    enhanced_file = PROJECT_ROOT / "data" / "processed" / "enhanced_classification_results.json"
    if enhanced_file.exists():
        with open(enhanced_file, 'r', encoding='utf-8') as f:
            results['enhanced_classification'] = json.load(f)
    
    # Enhanced association rules results
    enhanced_assoc_file = PROJECT_ROOT / "data" / "processed" / "enhanced_gspan_mining_results.json"
    if enhanced_assoc_file.exists():
        with open(enhanced_assoc_file, 'r', encoding='utf-8') as f:
            results['enhanced_association'] = json.load(f)
    
    return results

def analyze_random_forest_accuracy(results):
    """分析Random Forest的100%准确率"""
    print("=" * 60)
    print("ANALYZING RANDOM FOREST 100% ACCURACY")
    print("=" * 60)
    
    if 'enhanced_classification' not in results:
        print("❌ Enhanced classification results not found")
        return
    
    enhanced = results['enhanced_classification']
    
    # Check ML results
    ml_results = enhanced.get('ml_comparison', {})
    rf_results = ml_results.get('Random Forest', {})
    
    if not rf_results:
        print("❌ Random Forest results not found")
        return
    
    rf_accuracy = rf_results.get('test_accuracy', 0)
    print(f"📊 Random Forest Test Accuracy: {rf_accuracy:.4f} ({rf_accuracy*100:.1f}%)")
    
    # Check if it's actually 100%
    if rf_accuracy >= 0.999:
        print("🚨 SUSPICIOUS: Random Forest achieved near-perfect accuracy!")
        print("\nPotential Issues:")
        print("1. 📊 Data Leakage - Features may contain information from test set")
        print("2. 🎯 Perfect Separation - Features may be too predictive")
        print("3. 📈 Overfitting - Model may have memorized training patterns")
        
        # Analyze feature engineering
        feature_info = enhanced.get('feature_engineering', {})
        num_features = feature_info.get('num_features', 0)
        graph_stats = enhanced.get('graph_stats', {})
        num_nodes = graph_stats.get('num_nodes', 0)
        
        print(f"\n📈 Feature Analysis:")
        print(f"   Features: {num_features}")
        print(f"   Nodes: {num_nodes}")
        print(f"   Feature-to-Node Ratio: {num_features/num_nodes:.2f}")
        
        if num_features/num_nodes > 0.5:
            print("⚠️  HIGH FEATURE-TO-NODE RATIO - Potential overfitting risk")
        
        # Check classification report
        class_report = rf_results.get('classification_report', {})
        if class_report:
            print(f"\n📋 Classification Report Analysis:")
            for class_name, metrics in class_report.items():
                if isinstance(metrics, dict) and 'precision' in metrics:
                    precision = metrics.get('precision', 0)
                    recall = metrics.get('recall', 0)
                    f1 = metrics.get('f1-score', 0)
                    support = metrics.get('support', 0)
                    
                    print(f"   {class_name}: P={precision:.3f}, R={recall:.3f}, F1={f1:.3f}, Support={support}")
                    
                    if precision >= 0.999 and recall >= 0.999:
                        print(f"      🚨 Perfect scores for {class_name} - highly suspicious!")
    else:
        print(f"✅ Random Forest accuracy ({rf_accuracy:.3f}) is reasonable")
    
    return rf_accuracy

def analyze_confidence_values(results):
    """分析置信度值是否超过100%"""
    print("\n" + "=" * 60)
    print("ANALYZING CONFIDENCE VALUES")
    print("=" * 60)
    
    if 'enhanced_association' not in results:
        print("❌ Enhanced association results not found")
        return
    
    enhanced_assoc = results['enhanced_association']
    
    # Look for association rules
    rules = enhanced_assoc.get('association_rules', [])
    
    if not rules:
        print("❌ No association rules found")
        return
    
    print(f"📊 Found {len(rules)} association rules")
    
    suspicious_rules = []
    
    for i, rule in enumerate(rules):
        confidence = rule.get('confidence', 0)
        lift = rule.get('lift', 0)
        support = rule.get('support', 0)
        antecedent = rule.get('antecedent', [])
        consequent = rule.get('consequent', [])
        
        print(f"\n📋 Rule {i+1}:")
        print(f"   {' + '.join(antecedent)} => {' + '.join(consequent)}")
        print(f"   Confidence: {confidence:.3f} ({confidence*100:.1f}%)")
        print(f"   Lift: {lift:.3f}")
        print(f"   Support: {support}")
        
        # Check for suspicious confidence values
        if confidence > 1.0:
            print(f"   🚨 INVALID CONFIDENCE: {confidence:.3f} > 1.0!")
            suspicious_rules.append({
                'rule_index': i,
                'confidence': confidence,
                'antecedent': antecedent,
                'consequent': consequent
            })
        elif confidence >= 0.999:
            print(f"   ⚠️  NEAR-PERFECT CONFIDENCE: {confidence:.3f}")
        else:
            print(f"   ✅ Valid confidence value")
    
    if suspicious_rules:
        print(f"\n🚨 FOUND {len(suspicious_rules)} RULES WITH INVALID CONFIDENCE!")
        print("\nMathematical Issue:")
        print("   Confidence = P(Consequent|Antecedent) = Support(A∪B) / Support(A)")
        print("   This is a conditional probability and MUST be ≤ 1.0")
        print("\nPossible Causes:")
        print("   1. 🧮 Calculation Error - Wrong formula implementation")
        print("   2. 📊 Data Issue - Incorrect support counting")
        print("   3. 🔄 Logic Error - Mixing up confidence with lift")
        
        for rule in suspicious_rules:
            print(f"\n   Rule: {' + '.join(rule['antecedent'])} => {' + '.join(rule['consequent'])}")
            print(f"   Invalid Confidence: {rule['confidence']:.3f}")
    else:
        print("\n✅ All confidence values are mathematically valid")
    
    return suspicious_rules

def check_data_leakage_indicators():
    """检查数据泄露的指标"""
    print("\n" + "=" * 60)
    print("CHECKING DATA LEAKAGE INDICATORS")
    print("=" * 60)
    
    # Load the raw data to understand the structure
    data_file = PROJECT_ROOT / "data" / "processed" / "extracted_entities_relations.json"
    
    if not data_file.exists():
        print("❌ Data file not found")
        return
    
    with open(data_file, 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    relations = data.get('relations', [])
    
    print(f"📊 Dataset Analysis:")
    print(f"   Total Relations: {len(relations)}")
    
    # Analyze relation types
    relation_types = {}
    for rel in relations:
        rel_type = rel.get('type', 'Unknown')
        relation_types[rel_type] = relation_types.get(rel_type, 0) + 1
    
    print(f"   Relation Types:")
    for rel_type, count in relation_types.items():
        print(f"      {rel_type}: {count}")
    
    # Check for potential leakage patterns
    print(f"\n🔍 Data Leakage Risk Assessment:")
    
    # 1. Small dataset size
    if len(relations) < 200:
        print(f"   ⚠️  SMALL DATASET: {len(relations)} relations may lead to overfitting")
    
    # 2. Perfect class separation
    belongs_to_count = relation_types.get('BELONGS_TO', 0)
    authored_by_count = relation_types.get('AUTHORED_BY', 0)
    
    if belongs_to_count > 0 and authored_by_count > 0:
        ratio = belongs_to_count / authored_by_count
        print(f"   📊 BELONGS_TO/AUTHORED_BY Ratio: {ratio:.2f}")
        
        if ratio == 1.0:
            print(f"   🚨 PERFECT RATIO: May indicate artificial data structure")
    
    # 3. Feature engineering on full graph
    print(f"   ⚠️  GLOBAL FEATURES: Centrality measures computed on full graph")
    print(f"      This includes test nodes and may cause data leakage!")
    
    return relation_types

def generate_recommendations():
    """生成修复建议"""
    print("\n" + "=" * 60)
    print("RECOMMENDATIONS FOR FIXING ISSUES")
    print("=" * 60)
    
    print("🔧 For Random Forest 100% Accuracy:")
    print("   1. 🔄 Implement proper train/test split BEFORE feature engineering")
    print("   2. 📊 Compute centrality measures only on training subgraph")
    print("   3. 🎯 Use stratified sampling to ensure balanced classes")
    print("   4. 📈 Add cross-validation to detect overfitting")
    print("   5. 🧪 Test on completely unseen data")
    
    print("\n🔧 For Invalid Confidence Values:")
    print("   1. 🧮 Review confidence calculation formula:")
    print("      confidence = support(A ∪ B) / support(A)")
    print("   2. 📊 Verify support counting is correct")
    print("   3. 🔍 Check for division by zero or negative values")
    print("   4. 🔄 Ensure antecedent support ≥ full pattern support")
    print("   5. 🧪 Add validation: assert 0 ≤ confidence ≤ 1")
    
    print("\n🔧 General Data Science Best Practices:")
    print("   1. 📊 Use larger, more diverse datasets")
    print("   2. 🎯 Implement proper evaluation protocols")
    print("   3. 📈 Report confidence intervals")
    print("   4. 🧪 Validate results on external datasets")
    print("   5. 👥 Have results reviewed by domain experts")

def main():
    """主函数"""
    print("Suspicious Results Validator")
    print("Checking for data leakage and mathematical errors")
    
    # Load results
    results = load_results()
    
    if not results:
        print("❌ No results files found")
        return
    
    # Analyze Random Forest accuracy
    rf_accuracy = analyze_random_forest_accuracy(results)
    
    # Analyze confidence values
    suspicious_rules = analyze_confidence_values(results)
    
    # Check data leakage indicators
    relation_types = check_data_leakage_indicators()
    
    # Generate recommendations
    generate_recommendations()
    
    # Summary
    print("\n" + "=" * 60)
    print("VALIDATION SUMMARY")
    print("=" * 60)
    
    issues_found = 0
    
    if rf_accuracy and rf_accuracy >= 0.999:
        print("🚨 ISSUE 1: Random Forest 100% accuracy - likely data leakage")
        issues_found += 1
    
    if suspicious_rules:
        print(f"🚨 ISSUE 2: {len(suspicious_rules)} rules with invalid confidence > 100%")
        issues_found += 1
    
    if issues_found == 0:
        print("✅ No major issues detected in the results")
    else:
        print(f"\n⚠️  TOTAL ISSUES FOUND: {issues_found}")
        print("   These results should be treated with caution")
        print("   Recommend implementing the suggested fixes")

if __name__ == "__main__":
    main()
