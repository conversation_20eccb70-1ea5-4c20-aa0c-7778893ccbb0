"""
Enhanced Knowledge Graph Mining - Main Runner
运行增强版本的知识图谱挖掘任务，包含先进的分类和关联规则挖掘
"""

import os
import sys
from pathlib import Path
import time
import json

# Project setup
PROJECT_ROOT = Path(__file__).parent.absolute()
sys.path.insert(0, str(PROJECT_ROOT))

def check_dependencies():
    """检查关键依赖是否已安装"""
    print("Checking dependencies...")
    
    missing_deps = []
    
    try:
        import torch
        print(f"[OK] PyTorch: {torch.__version__}")
    except ImportError:
        missing_deps.append("torch")
    
    try:
        import torch_geometric
        print(f"[OK] PyTorch Geometric: {torch_geometric.__version__}")
    except ImportError:
        missing_deps.append("torch-geometric")
    
    try:
        import gspan_mining
        print("[OK] gspan-mining: Available")
    except ImportError:
        missing_deps.append("gspan-mining")
    
    try:
        import networkx as nx
        print(f"[OK] NetworkX: {nx.__version__}")
    except ImportError:
        missing_deps.append("networkx")
    
    try:
        import sklearn
        print(f"[OK] Scikit-learn: {sklearn.__version__}")
    except ImportError:
        missing_deps.append("scikit-learn")
    
    if missing_deps:
        print(f"\n[ERROR] Missing dependencies: {', '.join(missing_deps)}")
        print("Please install them using: pip install " + ' '.join(missing_deps))
        return False
    
    print("[OK] All dependencies are available!")
    return True

def run_enhanced_classification():
    """运行增强版分类任务"""
    print("\n" + "="*60)
    print("RUNNING ENHANCED GRAPH CLASSIFICATION")
    print("="*60)
    
    try:
        from src.data_mining.classification_enhanced import run_enhanced_classification
        
        start_time = time.time()
        classifier = run_enhanced_classification()
        end_time = time.time()
        
        if classifier:
            print(f"\n[OK] Enhanced classification completed in {end_time - start_time:.2f} seconds")
            return True
        else:
            print("[ERROR] Enhanced classification failed")
            return False
            
    except Exception as e:
        print(f"[ERROR] Error in enhanced classification: {e}")
        return False

def run_enhanced_association_rules():
    """运行增强版关联规则挖掘"""
    print("\n" + "="*60)
    print("RUNNING ENHANCED ASSOCIATION RULES MINING")
    print("="*60)
    
    try:
        from src.data_mining.association_rules_enhanced import run_enhanced_gspan_mining
        
        start_time = time.time()
        miner = run_enhanced_gspan_mining()
        end_time = time.time()
        
        if miner:
            print(f"\n[OK] Enhanced association rules mining completed in {end_time - start_time:.2f} seconds")
            return True
        else:
            print("[ERROR] Enhanced association rules mining failed")
            return False
            
    except Exception as e:
        print(f"[ERROR] Error in enhanced association rules: {e}")
        return False

def run_original_comparison():
    """运行原始版本进行对比"""
    print("\n" + "="*60)
    print("RUNNING ORIGINAL VERSIONS FOR COMPARISON")
    print("="*60)
    
    # Run original classification
    try:
        from src.data_mining.classification import run_node_classification
        print("\n--- Original Classification ---")
        original_classifier = run_node_classification()
        if original_classifier:
            print("[OK] Original classification completed")
        else:
            print("[ERROR] Original classification failed")
    except Exception as e:
        print(f"[ERROR] Error in original classification: {e}")
    
    # Run original association rules
    try:
        from src.data_mining.association_rules import run_frequent_subgraph_mining
        print("\n--- Original Association Rules ---")
        original_miner = run_frequent_subgraph_mining()
        if original_miner:
            print("[OK] Original association rules completed")
        else:
            print("[ERROR] Original association rules failed")
    except Exception as e:
        print(f"[ERROR] Error in original association rules: {e}")

def compare_results():
    """比较增强版本和原始版本的结果"""
    print("\n" + "="*60)
    print("COMPARING ENHANCED VS ORIGINAL RESULTS")
    print("="*60)
    
    # Load and compare classification results
    enhanced_class_file = PROJECT_ROOT / "data" / "processed" / "enhanced_classification_results.json"
    original_class_file = PROJECT_ROOT / "data" / "processed" / "classification_results.json"
    
    if enhanced_class_file.exists() and original_class_file.exists():
        print("\n--- Classification Comparison ---")
        
        with open(enhanced_class_file, 'r', encoding='utf-8') as f:
            enhanced_class = json.load(f)
        
        with open(original_class_file, 'r', encoding='utf-8') as f:
            original_class = json.load(f)
        
        enhanced_acc = enhanced_class.get('best_accuracy', 0)
        original_acc = original_class.get('accuracy', 0)
        
        print(f"Enhanced Classification Best Accuracy: {enhanced_acc:.4f}")
        print(f"Original Classification Accuracy: {original_acc:.4f}")
        print(f"Improvement: {((enhanced_acc - original_acc) / original_acc * 100):.2f}%")
        
        print(f"\nEnhanced Features: {enhanced_class.get('feature_engineering', {}).get('num_features', 'N/A')}")
        print(f"Original Features: {original_class.get('node_features_dim', 'N/A')}")
        
        print(f"\nEnhanced Models: {', '.join(enhanced_class.get('gnn_results', {}).keys())}")
        print(f"Original Method: {original_class.get('method', 'Unknown')}")
    
    # Load and compare association rules results
    enhanced_assoc_file = PROJECT_ROOT / "data" / "processed" / "enhanced_gspan_mining_results.json"
    original_assoc_file = PROJECT_ROOT / "data" / "processed" / "frequent_subgraph_mining_results.json"
    
    if enhanced_assoc_file.exists() and original_assoc_file.exists():
        print("\n--- Association Rules Comparison ---")
        
        with open(enhanced_assoc_file, 'r', encoding='utf-8') as f:
            enhanced_assoc = json.load(f)
        
        with open(original_assoc_file, 'r', encoding='utf-8') as f:
            original_assoc = json.load(f)
        
        enhanced_patterns = enhanced_assoc.get('comprehensive_report', {}).get('mining_summary', {}).get('total_frequent_subgraphs', 0)
        enhanced_rules = enhanced_assoc.get('comprehensive_report', {}).get('mining_summary', {}).get('total_association_rules', 0)
        
        # Original version results are nested differently
        original_patterns = len(original_assoc.get('results', {}).get('frequent_node_patterns', {}))
        original_rules = len(original_assoc.get('results', {}).get('association_rules', []))
        
        print(f"Enhanced Method: {enhanced_assoc.get('algorithm', 'Unknown')}")
        print(f"Original Method: {original_assoc.get('mining_method', 'Unknown')}")
        
        print(f"\nEnhanced Frequent Patterns: {enhanced_patterns}")
        print(f"Original Frequent Patterns: {original_patterns}")
        
        print(f"\nEnhanced Association Rules: {enhanced_rules}")
        print(f"Original Association Rules: {original_rules}")

def generate_improvement_summary():
    """生成改进总结报告"""
    print("\n" + "="*60)
    print("IMPROVEMENT SUMMARY REPORT")
    print("="*60)
    
    improvements = {
        "Classification Enhancements": [
            "[+] Multiple GNN models (GraphSAGE, GAT, GCN)",
            "[+] Rich feature engineering (20+ features per node)",
            "[+] Advanced centrality measures",
            "[+] Cross-validation and early stopping",
            "[+] Comprehensive model comparison",
            "[+] Traditional ML baseline comparison"
        ],
        "Association Rules Enhancements": [
            "[+] True gSpan frequent subgraph mining",
            "[+] Multiple graph database creation methods",
            "[+] Advanced pattern analysis and motif detection",
            "[+] Semantic pattern recognition",
            "[+] High-quality association rule generation",
            "[+] Comprehensive insights generation"
        ],
        "Technical Improvements": [
            "[+] Full utilization of PyTorch Geometric",
            "[+] Integration of gspan-mining library",
            "[+] Advanced graph feature engineering",
            "[+] Professional visualization",
            "[+] Comprehensive result analysis",
            "[+] Automated comparison and reporting"
        ]
    }
    
    for category, items in improvements.items():
        print(f"\n{category}:")
        for item in items:
            print(f"  {item}")
    
    print(f"\n{'='*60}")
    print("NEXT STEPS RECOMMENDATIONS:")
    print("="*60)
    
    next_steps = [
        "1. 实验不同的gSpan参数以发现更多模式",
        "2. 尝试其他图神经网络架构(如GraphTransformer)",
        "3. 集成更多的图特征(如node2vec embeddings)",
        "4. 使用更大的数据集进行验证",
        "5. 开发交互式可视化工具",
        "6. 部署为Web应用供实际使用"
    ]
    
    for step in next_steps:
        print(f"  {step}")

def main():
    """主运行函数"""
    print("Enhanced Knowledge Graph Mining System")
    print("="*60)
    
    # Check dependencies
    if not check_dependencies():
        return
    
    # Check if data exists
    data_file = PROJECT_ROOT / "data" / "processed" / "extracted_entities_relations.json"
    if not data_file.exists():
        print(f"\n[ERROR] Data file not found: {data_file}")
        print("Please ensure you have run the data extraction process first.")
        return
    
    print(f"\n[OK] Data file found: {data_file}")
    
    # Get user choice
    print("\nSelect what to run:")
    print("1. Enhanced Classification only")
    print("2. Enhanced Association Rules only") 
    print("3. Both Enhanced methods")
    print("4. Both Enhanced + Original (for comparison)")
    print("5. Full analysis with comparison report")
    
    choice = input("\nEnter your choice (1-5): ").strip()
    
    if choice == "1":
        run_enhanced_classification()
    elif choice == "2":
        run_enhanced_association_rules()
    elif choice == "3":
        run_enhanced_classification()
        run_enhanced_association_rules()
    elif choice == "4":
        run_enhanced_classification()
        run_enhanced_association_rules()
        run_original_comparison()
        compare_results()
    elif choice == "5":
        run_enhanced_classification()
        run_enhanced_association_rules()
        run_original_comparison()
        compare_results()
        generate_improvement_summary()
    else:
        print("Invalid choice. Running enhanced methods by default...")
        run_enhanced_classification()
        run_enhanced_association_rules()
    
    print("\n" + "="*60)
    print("ENHANCED KNOWLEDGE GRAPH MINING COMPLETE!")
    print("="*60)
    print("\nResults can be found in:")
    print("  [+] data/processed/ (JSON results)")
    print("  [+] data/graphs/ (Visualizations)")
    print("\nThe enhanced versions provide:")
    print("  [+] Better accuracy through advanced models")
    print("  [+] Deeper insights through rich features")
    print("  [+] True frequent subgraph mining")
    print("  [+] Professional analysis and reporting")

if __name__ == "__main__":
    main()
