#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Safe Visualization Runner (Fixed)
安全可视化运行器，避免编码问题
"""

import os
import sys
import time
import traceback
from pathlib import Path

# 添加项目路径
PROJECT_ROOT = Path(__file__).parent.absolute()
sys.path.insert(0, str(PROJECT_ROOT))

def run_safe_visualization(module_name, function_name):
    """安全运行可视化模块"""
    try:
        print(f"Running {module_name}.{function_name} in safe mode...")
        
        # 动态导入模块
        module = __import__(module_name, fromlist=[function_name])
        func = getattr(module, function_name)
        
        # 运行函数
        start_time = time.time()
        result = func()
        duration = time.time() - start_time
        
        print(f"SUCCESS: {module_name}.{function_name} completed in {duration:.2f}s")
        return True
        
    except ImportError as e:
        print(f"IMPORT ERROR in {module_name}: {e}")
        return False
    except AttributeError as e:
        print(f"FUNCTION ERROR in {module_name}: {e}")
        return False
    except Exception as e:
        print(f"ERROR in {module_name}: {e}")
        # 打印详细错误信息（仅供调试）
        if hasattr(e, '__traceback__'):
            traceback.print_exc()
        return False

def run_basic_visualizations():
    """运行基础可视化"""
    print("RUNNING BASIC VISUALIZATIONS")
    print("=" * 50)
    
    visualizations = [
        ("src.data_mining.clustering", "run_author_clustering"),
        ("src.data_mining.classification_enhanced_fixed", "run_fixed_classification"),
    ]
    
    results = {}
    
    for module, function in visualizations:
        success = run_safe_visualization(module, function)
        results[f"{module}.{function}"] = success
        time.sleep(1)  # 短暂暂停避免资源冲突
    
    return results

def generate_visualization_summary():
    """生成可视化总结"""
    print("\nVISUALIZATION SUMMARY")
    print("=" * 50)
    
    graphs_dir = PROJECT_ROOT / "data" / "graphs"
    
    if graphs_dir.exists():
        graph_files = list(graphs_dir.glob("*.png"))
        print(f"Generated visualization files: {len(graph_files)}")
        
        for graph_file in sorted(graph_files):
            file_size = graph_file.stat().st_size
            print(f"  * {graph_file.name} ({file_size:,} bytes)")
    else:
        print("No graphs directory found")
    
    return graphs_dir.exists()

def main():
    """主函数"""
    print("SAFE VISUALIZATION RUNNER")
    print("=" * 50)
    print("Running visualization modules with error handling")
    print()
    
    # 运行基础可视化
    vis_results = run_basic_visualizations()
    
    # 生成总结
    has_graphs = generate_visualization_summary()
    
    # 最终状态
    print(f"\nFINAL STATUS")
    print("=" * 50)
    
    successful_vis = sum(vis_results.values())
    total_vis = len(vis_results)
    
    print(f"Visualization modules: {successful_vis}/{total_vis} successful")
    print(f"Graph files available: {'YES' if has_graphs else 'NO'}")
    
    for module, success in vis_results.items():
        status = "SUCCESS" if success else "FAILED"
        print(f"  {module}: {status}")
    
    if successful_vis > 0 or has_graphs:
        print(f"\nVisualization pipeline completed!")
        print(f"Check data/graphs/ directory for generated charts")
    else:
        print(f"\nVisualization pipeline had issues")
        print(f"Check error messages above for details")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\nOperation cancelled by user")
    except Exception as e:
        print(f"Unexpected error: {e}")
        traceback.print_exc()
