"""
Complete Visualization Generator for Knowledge Graph Mining Project
运行所有数据挖掘模块并生成完整的可视化图像集合
"""

import os
import sys
from pathlib import Path
import time
import json

# Project setup
PROJECT_ROOT = Path(__file__).parent.absolute()
sys.path.insert(0, str(PROJECT_ROOT))

def check_dependencies():
    """检查关键依赖是否已安装"""
    print("Checking dependencies...")
    
    missing_deps = []
    
    try:
        import torch
        print(f"[OK] PyTorch: {torch.__version__}")
    except ImportError:
        missing_deps.append("torch")
    
    try:
        import torch_geometric
        print(f"[OK] PyTorch Geometric: {torch_geometric.__version__}")
    except ImportError:
        missing_deps.append("torch-geometric")
    
    try:
        import gspan_mining
        print("[OK] gspan-mining: Available")
    except ImportError:
        missing_deps.append("gspan-mining")
    
    try:
        import networkx as nx
        print(f"[OK] NetworkX: {nx.__version__}")
    except ImportError:
        missing_deps.append("networkx")
    
    try:
        import sklearn
        print(f"[OK] Scikit-learn: {sklearn.__version__}")
    except ImportError:
        missing_deps.append("scikit-learn")
    
    try:
        import matplotlib.pyplot as plt
        print(f"[OK] Matplotlib: Available")
    except ImportError:
        missing_deps.append("matplotlib")
    
    if missing_deps:
        print(f"\n[ERROR] Missing dependencies: {', '.join(missing_deps)}")
        print("Please install them using: pip install " + ' '.join(missing_deps))
        return False
    
    print("[OK] All dependencies are available!")
    return True

def run_enhanced_classification():
    """运行增强版分类任务"""
    print("\n" + "="*60)
    print("RUNNING ENHANCED GRAPH CLASSIFICATION")
    print("="*60)
    
    try:
        from src.data_mining.classification_enhanced import run_enhanced_classification
        
        start_time = time.time()
        classifier = run_enhanced_classification()
        end_time = time.time()
        
        if classifier:
            print(f"\n[OK] Enhanced classification completed in {end_time - start_time:.2f} seconds")
            print("Generated: enhanced_classification_results.png")
            return True
        else:
            print("[ERROR] Enhanced classification failed")
            return False
            
    except Exception as e:
        print(f"[ERROR] Error in enhanced classification: {e}")
        return False

def run_original_classification():
    """运行原始分类任务"""
    print("\n" + "="*60)
    print("RUNNING ORIGINAL GRAPH CLASSIFICATION")
    print("="*60)
    
    try:
        from src.data_mining.classification import run_node_classification
        
        start_time = time.time()
        classifier = run_node_classification()
        end_time = time.time()
        
        if classifier:
            print(f"\n[OK] Original classification completed in {end_time - start_time:.2f} seconds")
            print("Generated: classification_result.png")
            return True
        else:
            print("[ERROR] Original classification failed")
            return False
            
    except Exception as e:
        print(f"[ERROR] Error in original classification: {e}")
        return False

def run_enhanced_association_rules():
    """运行增强版关联规则挖掘"""
    print("\n" + "="*60)
    print("RUNNING ENHANCED ASSOCIATION RULES MINING")
    print("="*60)
    
    try:
        from src.data_mining.association_rules_enhanced import run_enhanced_gspan_mining
        
        start_time = time.time()
        miner = run_enhanced_gspan_mining()
        end_time = time.time()
        
        if miner:
            print(f"\n[OK] Enhanced association rules mining completed in {end_time - start_time:.2f} seconds")
            print("Generated: frequent_subgraphs_gspan.png")
            return True
        else:
            print("[ERROR] Enhanced association rules mining failed")
            return False
            
    except Exception as e:
        print(f"[ERROR] Error in enhanced association rules: {e}")
        return False

def run_original_association_rules():
    """运行原始关联规则挖掘"""
    print("\n" + "="*60)
    print("RUNNING ORIGINAL ASSOCIATION RULES MINING")
    print("="*60)
    
    try:
        from src.data_mining.association_rules import run_frequent_subgraph_mining
        
        start_time = time.time()
        miner = run_frequent_subgraph_mining()
        end_time = time.time()
        
        if miner:
            print(f"\n[OK] Original association rules mining completed in {end_time - start_time:.2f} seconds")
            print("Generated: frequent_node_patterns.png, frequent_edge_patterns.png")
            return True
        else:
            print("[ERROR] Original association rules mining failed")
            return False
            
    except Exception as e:
        print(f"[ERROR] Error in original association rules: {e}")
        return False

def run_clustering_analysis():
    """运行聚类分析"""
    print("\n" + "="*60)
    print("RUNNING CLUSTERING ANALYSIS")
    print("="*60)
    
    try:
        from src.data_mining.clustering import run_author_clustering, run_paper_topic_clustering
        
        # Run author clustering
        print("\n--- Author Collaboration Clustering ---")
        start_time = time.time()
        author_analyzer = run_author_clustering()
        end_time = time.time()
        
        if author_analyzer:
            print(f"[OK] Author clustering completed in {end_time - start_time:.2f} seconds")
            print("Generated: author_collaboration_clusters.png")
        else:
            print("[ERROR] Author clustering failed")
        
        # Run paper-topic clustering
        print("\n--- Paper-Topic Clustering ---")
        start_time = time.time()
        topic_analyzer = run_paper_topic_clustering()
        end_time = time.time()
        
        if topic_analyzer:
            print(f"[OK] Paper-topic clustering completed in {end_time - start_time:.2f} seconds")
            print("Generated: paper_topic_clusters.png")
            return True
        else:
            print("[ERROR] Paper-topic clustering failed")
            return False
            
    except Exception as e:
        print(f"[ERROR] Error in clustering analysis: {e}")
        return False

def test_font_configuration():
    """测试字体配置"""
    print("\n" + "="*60)
    print("TESTING FONT CONFIGURATION")
    print("="*60)
    
    try:
        from src.utils.font_config import test_chinese_display
        
        test_path = test_chinese_display()
        if test_path:
            print(f"[OK] Font test completed")
            print(f"Generated: font_test.png")
            return True
        else:
            print("[ERROR] Font test failed")
            return False
            
    except Exception as e:
        print(f"[ERROR] Error in font test: {e}")
        return False

def check_generated_images():
    """检查生成的图像文件"""
    print("\n" + "="*60)
    print("CHECKING GENERATED IMAGES")
    print("="*60)
    
    graphs_dir = PROJECT_ROOT / "data" / "graphs"
    if not graphs_dir.exists():
        print("[ERROR] Graphs directory does not exist")
        return []
    
    image_files = list(graphs_dir.glob("*.png"))
    
    print(f"Found {len(image_files)} image files:")
    for img_file in sorted(image_files):
        file_size = img_file.stat().st_size / 1024  # KB
        print(f"  [+] {img_file.name} ({file_size:.1f} KB)")
    
    return image_files

def main():
    """主运行函数"""
    print("Complete Knowledge Graph Mining Visualization Generator")
    print("="*60)
    
    # Check dependencies
    if not check_dependencies():
        return
    
    # Check if data exists
    data_file = PROJECT_ROOT / "data" / "processed" / "extracted_entities_relations.json"
    if not data_file.exists():
        print(f"\n[ERROR] Data file not found: {data_file}")
        print("Please ensure you have run the data extraction process first.")
        return
    
    print(f"\n[OK] Data file found: {data_file}")
    
    # Track success of each module
    results = {}
    
    # Run all visualization modules
    print("\n" + "="*60)
    print("RUNNING ALL VISUALIZATION MODULES")
    print("="*60)
    
    # 1. Enhanced Classification
    results['enhanced_classification'] = run_enhanced_classification()
    
    # 2. Original Classification  
    results['original_classification'] = run_original_classification()
    
    # 3. Enhanced Association Rules
    results['enhanced_association'] = run_enhanced_association_rules()
    
    # 4. Original Association Rules
    results['original_association'] = run_original_association_rules()
    
    # 5. Clustering Analysis
    results['clustering'] = run_clustering_analysis()
    
    # 6. Font Configuration Test
    results['font_test'] = test_font_configuration()
    
    # Check generated images
    generated_images = check_generated_images()
    
    # Summary
    print("\n" + "="*60)
    print("VISUALIZATION GENERATION COMPLETE!")
    print("="*60)
    
    successful_modules = sum(1 for success in results.values() if success)
    total_modules = len(results)
    
    print(f"\nModule Success Rate: {successful_modules}/{total_modules}")
    
    for module, success in results.items():
        status = "[OK]" if success else "[FAILED]"
        print(f"  {status} {module}")
    
    print(f"\nTotal Images Generated: {len(generated_images)}")
    
    if len(generated_images) >= 7:
        print("\n🎉 SUCCESS: All expected visualizations have been generated!")
    else:
        print(f"\n⚠️  WARNING: Expected at least 7 images, but only {len(generated_images)} were generated.")
    
    print("\nResults can be found in:")
    print("  [+] data/processed/ (JSON results)")
    print("  [+] data/graphs/ (Visualizations)")

if __name__ == "__main__":
    main()
