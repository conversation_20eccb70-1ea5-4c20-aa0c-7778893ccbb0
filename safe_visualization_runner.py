"""
Safe Visualization Runner
安全的可视化运行器，避免plt.show()挂起问题
"""

import os
import sys
from pathlib import Path
import matplotlib
import matplotlib.pyplot as plt

# Project setup
PROJECT_ROOT = Path(__file__).parent.parent.absolute()
sys.path.insert(0, str(PROJECT_ROOT))

# Force headless mode
matplotlib.use('Agg')

# Disable plt.show() to prevent hanging
def safe_show(*args, **kwargs):
    """Safe show function that doesn't hang"""
    print("plt.show() suppressed (safe mode)")
    pass

plt.show = safe_show

def run_safe_visualization(module_name, function_name):
    """安全运行可视化函数"""
    print(f"Running {module_name}.{function_name} in safe mode...")
    
    try:
        # Import and run the function
        if module_name == "classification":
            from src.data_mining.classification import run_node_classification
            result = run_node_classification()
        elif module_name == "association_rules":
            from src.data_mining.association_rules import run_frequent_subgraph_mining
            result = run_frequent_subgraph_mining()
        elif module_name == "clustering_author":
            from src.data_mining.clustering import run_author_clustering
            result = run_author_clustering()
        elif module_name == "clustering_topic":
            from src.data_mining.clustering import run_paper_topic_clustering
            result = run_paper_topic_clustering()
        elif module_name == "font_test":
            from src.utils.font_config import test_chinese_display
            result = test_chinese_display()
        else:
            print(f"Unknown module: {module_name}")
            return False
        
        if result:
            print(f"✅ {module_name} completed successfully")
            return True
        else:
            print(f"❌ {module_name} failed")
            return False
            
    except Exception as e:
        print(f"❌ Error in {module_name}: {e}")
        return False

def main():
    """主函数"""
    print("Safe Visualization Runner")
    print("=" * 50)
    
    modules_to_run = [
        ("classification", "run_node_classification"),
        ("association_rules", "run_frequent_subgraph_mining"), 
        ("clustering_author", "run_author_clustering"),
        ("clustering_topic", "run_paper_topic_clustering"),
        ("font_test", "test_chinese_display")
    ]
    
    results = {}
    
    for module, function in modules_to_run:
        results[module] = run_safe_visualization(module, function)
    
    # Summary
    print("\n" + "=" * 50)
    print("SAFE VISUALIZATION RESULTS")
    print("=" * 50)
    
    successful = sum(1 for success in results.values() if success)
    total = len(results)
    
    for module, success in results.items():
        status = "✅" if success else "❌"
        print(f"{status} {module}")
    
    print(f"\nSuccess Rate: {successful}/{total}")

if __name__ == "__main__":
    main()
