# 知识图谱挖掘项目

基于ArXiv论文数据的知识图谱构建与数据挖掘系统

## 项目结构

```
knowledge-graph-mining/
├── src/
│   ├── data_acquisition/      # 数据获取模块 (使用crawl4ai MCP)
│   ├── knowledge_construction/ # 知识图谱构建模块
│   ├── data_mining/           # 数据挖掘模块
│   └── application/           # 应用服务模块
├── data/
│   ├── raw/                   # 原始数据
│   ├── processed/             # 处理后数据
│   └── graphs/                # 图数据
├── configs/                   # 配置文件
├── notebooks/                 # Jupyter notebooks
├── tests/                     # 测试文件
└── docs/                      # 文档
```

## 快速开始

### 1. 环境配置

```bash
# 安装Python依赖
pip install -r requirements.txt

# 下载spaCy英文模型
python -m spacy download en_core_web_sm

# 启动Neo4j数据库
docker-compose up -d
```

### 2. 运行项目

```bash
# 检查环境和显示说明
python main.py

# 数据抓取 (使用crawl4ai MCP)
# 需要在支持MCP的环境中抓取以下URL:
# - https://arxiv.org/abs/2301.00001
# - https://arxiv.org/abs/2301.00123
# - https://arxiv.org/abs/2302.01234

# 知识图谱构建
python src/knowledge_construction/extractor.py
python src/knowledge_construction/loader.py

# 数据挖掘 (即将实现)
python src/data_mining/clustering.py
python src/data_mining/classification.py
python src/data_mining/association_rules.py
```

### 3. 访问Neo4j浏览器

打开浏览器访问: http://localhost:7474
- 用户名: neo4j
- 密码: password

## 核心特性

1. **数据获取**: 使用crawl4ai MCP抓取ArXiv论文数据
2. **知识图谱构建**: 基于结构化数据和NLP方法提取实体关系
3. **图数据挖掘**: 
   - 聚类分析 (社区发现)
   - 分类预测 (图神经网络)
   - 关联规则 (频繁子图挖掘)
4. **图数据库**: Neo4j存储和查询

## 技术栈

- **Python**: 核心开发语言
- **Neo4j**: 图数据库
- **spaCy**: 自然语言处理
- **PyTorch Geometric**: 图神经网络
- **NetworkX**: 图分析
- **FastAPI**: Web API
- **Streamlit**: 前端界面

## 项目状态

- [x] 项目架构设计
- [x] 数据获取模块
- [x] 知识图谱构建模块
- [ ] 数据挖掘模块 (开发中)
- [ ] 应用服务模块 (计划中)

## 使用说明

1. 确保Docker已安装并启动Neo4j
2. 在支持crawl4ai MCP的环境中抓取论文数据
3. 运行知识图谱构建流程
4. 执行数据挖掘任务

## 注意事项

- 项目设计为不依赖大语言模型(LLM)
- 使用传统NLP方法和结构化数据提取
- 适合大学课程项目要求
- Windows环境下使用cmd而非PowerShell
