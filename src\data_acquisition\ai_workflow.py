"""
智能数据获取工作流程
在Claude Desktop对话中直接实现AI驱动的数据获取
"""

# =============================================================================
# AI智能数据获取工作流程提示词
# =============================================================================

RESEARCH_ANALYSIS_PROMPT = """
我是一个智能数据获取助手。当用户提出研究需求时，我会：

1. 【需求分析】
   - 识别研究领域和关键概念
   - 确定所需数据类型
   - 评估研究深度和广度

2. 【数据源策略】
   - 根据研究领域选择最佳数据源
   - 优先考虑权威学术网站
   - 平衡数据质量与获取难度

3. 【爬取执行】
   - 使用crawl4ai MCP工具
   - 针对性抓取相关内容
   - 智能过滤无关信息

4. 【数据处理】
   - 自动提取实体关系
   - 整合到现有知识图谱
   - 更新数据挖掘结果

用户需求格式：
"我想研究[主题]在[领域]中的[应用/发展/趋势]"

例如：
- "我想研究图神经网络在推荐系统中的应用"
- "我想了解量子计算在机器学习中的最新发展"
- "我想分析区块链技术在金融科技中的创新趋势"
"""

# =============================================================================
# 数据源映射策略
# =============================================================================

DOMAIN_MAPPING = {
    "machine_learning": {
        "keywords": ["机器学习", "深度学习", "神经网络", "AI", "人工智能"],
        "sources": [
            "https://arxiv.org/list/cs.LG/recent",  # Machine Learning
            "https://arxiv.org/list/cs.AI/recent",  # Artificial Intelligence
            "https://arxiv.org/list/stat.ML/recent" # Statistics - Machine Learning
        ],
        "focus": ["algorithms", "models", "applications", "theory"]
    },
    
    "computer_vision": {
        "keywords": ["计算机视觉", "图像处理", "目标检测", "图像分类"],
        "sources": [
            "https://arxiv.org/list/cs.CV/recent",  # Computer Vision
            "https://arxiv.org/list/eess.IV/recent" # Image and Video Processing
        ],
        "focus": ["image analysis", "video processing", "visual recognition"]
    },
    
    "nlp": {
        "keywords": ["自然语言处理", "文本挖掘", "语言模型", "NLP"],
        "sources": [
            "https://arxiv.org/list/cs.CL/recent",  # Computational Linguistics
            "https://arxiv.org/list/cs.IR/recent"   # Information Retrieval
        ],
        "focus": ["text analysis", "language understanding", "text generation"]
    },
    
    "blockchain": {
        "keywords": ["区块链", "加密货币", "智能合约", "DeFi"],
        "sources": [
            "https://arxiv.org/list/cs.CR/recent",  # Cryptography and Security
            "https://arxiv.org/list/q-fin.GN/recent" # General Finance
        ],
        "focus": ["security", "decentralization", "consensus", "applications"]
    },
    
    "quantum_computing": {
        "keywords": ["量子计算", "量子算法", "量子通信"],
        "sources": [
            "https://arxiv.org/list/quant-ph/recent", # Quantum Physics
            "https://arxiv.org/list/cs.ET/recent"     # Emerging Technologies
        ],
        "focus": ["quantum algorithms", "quantum hardware", "quantum applications"]
    },
    
    "bioinformatics": {
        "keywords": ["生物信息学", "基因组学", "蛋白质", "生物数据"],
        "sources": [
            "https://arxiv.org/list/q-bio.GN/recent", # Genomics
            "https://arxiv.org/list/q-bio.BM/recent"  # Biomolecules
        ],
        "focus": ["genomics", "proteomics", "computational biology"]
    }
}

# =============================================================================
# 智能内容处理策略
# =============================================================================

CONTENT_PROCESSING_STRATEGY = """
爬取到内容后的智能处理步骤：

1. 【内容分析】
   - 识别论文标题、作者、摘要
   - 提取关键词和主题
   - 判断内容相关性

2. 【实体提取】
   - 自动识别研究者姓名
   - 提取技术术语和概念
   - 发现机构和合作关系

3. 【关系构建】
   - 论文-作者关系
   - 作者-机构关系
   - 概念-应用关系
   - 引用-被引用关系

4. 【质量评估】
   - 评估内容权威性
   - 判断研究新颖性
   - 确定数据完整性

5. 【自动整合】
   - 合并到现有知识图谱
   - 更新实体关系数据
   - 重新运行数据挖掘分析
   - 生成增量分析报告
"""

# =============================================================================
# 实际实现示例
# =============================================================================

def intelligent_research_pipeline():
    """
    智能研究数据获取完整流程示例
    
    使用方法：
    1. 用户在Claude Desktop中说："我想研究图神经网络在推荐系统中的应用"
    2. AI分析需求，确定为machine_learning + recommendation_systems
    3. AI选择相应的arxiv分类页面
    4. AI调用crawl4ai:md工具抓取内容
    5. AI自动处理内容并整合到项目中
    """
    
    # 步骤1: AI分析用户需求
    user_query = "我想研究图神经网络在推荐系统中的应用"
    
    # 步骤2: AI识别关键词和领域
    identified_domain = "machine_learning"
    key_concepts = ["graph neural networks", "recommendation systems", "collaborative filtering"]
    
    # 步骤3: AI选择数据源
    target_urls = [
        "https://arxiv.org/list/cs.LG/recent",
        "https://arxiv.org/list/cs.IR/recent"
    ]
    
    # 步骤4: AI执行爬取 (通过MCP调用)
    # crawl4ai:md(url=target_urls[0], f="fit")
    
    # 步骤5: AI处理内容并整合
    # 自动调用项目的extractor.py和数据挖掘模块
    
    return "智能数据获取和处理完成"

if __name__ == "__main__":
    print("智能数据获取工作流程已设计完成")
    print("用户可以直接在Claude Desktop中提出研究需求")
    print("AI将自动完成从需求分析到数据整合的全流程")
