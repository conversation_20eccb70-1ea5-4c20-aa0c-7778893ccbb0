"""
Headless Matplotlib Configuration
无头环境matplotlib配置，避免GUI相关问题
"""

import matplotlib
import os

def setup_headless_matplotlib():
    """设置matplotlib为无头模式"""
    # 检查是否在无头环境中
    if os.environ.get('DISPLAY') is None and os.name != 'nt':
        # Linux/Unix无头环境
        matplotlib.use('Agg')
        print("Matplotlib set to headless mode (Agg backend)")
    elif os.environ.get('MPLBACKEND') == 'Agg':
        # 强制无头模式
        matplotlib.use('Agg')
        print("Matplotlib set to headless mode (forced)")
    else:
        # 正常GUI环境
        print("Matplotlib using default backend")

def disable_plt_show():
    """禁用plt.show()以避免挂起"""
    import matplotlib.pyplot as plt
    
    # 保存原始show函数
    original_show = plt.show
    
    def silent_show(*args, **kwargs):
        """静默的show函数，不显示GUI"""
        print("plt.show() called but suppressed (headless mode)")
        pass
    
    # 替换show函数
    plt.show = silent_show
    
    return original_show

# 自动设置
setup_headless_matplotlib()
