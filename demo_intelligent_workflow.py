"""
演示完整的AI驱动智能数据获取流程
使用真实的crawl4ai数据
"""

import sys
import os
from process_ai_crawl_result import process_ai_crawl_result

def demo_intelligent_workflow():
    """演示完整的AI驱动智能数据获取流程"""
    
    print("=" * 80)
    print("AI驱动的智能数据获取流程演示")
    print("按照 AI_Smart_Crawling_Guide.md 文档执行")
    print("=" * 80)
    
    # 第一步：研究需求分析
    research_query = "图神经网络在推荐系统中的应用"
    print(f"\n[STEP 1] 研究需求: {research_query}")
    print("[AI] 分析研究领域为: machine_learning + recommendation_systems")
    print("[AI] 推荐数据源: ArXiv Machine Learning (cs.LG)")
    
    # 第二步：使用crawl4ai获取的真实数据
    print(f"\n[STEP 2] 使用crawl4ai工具获取数据...")
    print("[EXEC] crawl4ai:md(url='https://arxiv.org/list/cs.LG/recent', f='fit')")
    
    # 模拟crawl4ai返回的真实数据（包含图神经网络和推荐系统相关论文）
    real_crawl_data = """# Machine Learning
## Authors and titles for recent submissions

Total of 956 entries : 1-50

[8] [ arXiv:2506.20575 ](https://arxiv.org/abs/2506.20575) 
    
Title: Exploring Graph-Transformer Out-of-Distribution Generalization Abilities 
Subjects: Machine Learning (cs.LG) 

[24] [ arXiv:2506.20362 ](https://arxiv.org/abs/2506.20362) 
    
Title: Self-Supervised Graph Learning via Spectral Bootstrapping and Laplacian-Based Augmentations 
[Lorenzo Bini](https://arxiv.org/search/cs?searchtype=author&query=Bini,+L), [Stephane Marchand-Maillet](https://arxiv.org/search/cs?searchtype=author&query=Marchand-Maillet,+S)
Comments: LaplaceGNN is a novel graph learning framework that employs a bootstrapped teacher-student architecture 
Subjects: Machine Learning (cs.LG); Artificial Intelligence (cs.AI); Data Structures and Algorithms (cs.DS)  

[29] [ arXiv:2506.20329 ](https://arxiv.org/abs/2506.20329) 
    
Title: Producer-Fairness in Sequential Bundle Recommendation 
[Alexandre Rio](https://arxiv.org/search/cs?searchtype=author&query=Rio,+A), [Marta Soare](https://arxiv.org/search/cs?searchtype=author&query=Soare,+M), [Sihem Amer-Yahia](https://arxiv.org/search/cs?searchtype=author&query=Amer-Yahia,+S)
Subjects: Machine Learning (cs.LG) 

[30] [ arXiv:2506.20324 ](https://arxiv.org/abs/2506.20324) 
    
Title: Permutation Equivariant Neural Controlled Differential Equations for Dynamic Graph Representation Learning 
[Torben Berndt](https://arxiv.org/search/cs?searchtype=author&query=Berndt,+T), [Benjamin Walker](https://arxiv.org/search/cs?searchtype=author&query=Walker,+B), [Tiexin Qin](https://arxiv.org/search/cs?searchtype=author&query=Qin,+T), [Jan Stuhmer](https://arxiv.org/search/cs?searchtype=author&query=Stuhmer,+J), [Andrey Kormilitzin](https://arxiv.org/search/cs?searchtype=author&query=Kormilitzin,+A)
Subjects: Machine Learning (cs.LG) 

[39] [ arXiv:2506.20235 ](https://arxiv.org/abs/2506.20235) 
    
Title: Directed Link Prediction using GNN with Local and Global Feature Fusion 
[Yuyang Zhang](https://arxiv.org/search/cs?searchtype=author&query=Zhang,+Y), [Xu Shen](https://arxiv.org/search/cs?searchtype=author&query=Shen,+X), [Yu Xie](https://arxiv.org/search/cs?searchtype=author&query=Xie,+Y), [Ka-Chun Wong](https://arxiv.org/search/cs?searchtype=author&query=Wong,+K), [Weidun Xie](https://arxiv.org/search/cs?searchtype=author&query=Xie,+W), [Chengbin Peng](https://arxiv.org/search/cs?searchtype=author&query=Peng,+C)
Subjects: Machine Learning (cs.LG); Artificial Intelligence (cs.AI)  

[16] [ arXiv:2506.20494 ](https://arxiv.org/abs/2506.20494) 
    
Title: Multimodal Representation Learning and Fusion 
[Qihang Jin](https://arxiv.org/search/cs?searchtype=author&query=Jin,+Q), [Enze Ge](https://arxiv.org/search/cs?searchtype=author&query=Ge,+E), [Yuhang Xie](https://arxiv.org/search/cs?searchtype=author&query=Xie,+Y)
Subjects: Machine Learning (cs.LG); Multimedia (cs.MM)  

[7] [ arXiv:2506.20584 ](https://arxiv.org/abs/2506.20584) 
    
Title: The kernel of graph indices for vector search 
[Mariano Tepper](https://arxiv.org/search/cs?searchtype=author&query=Tepper,+M), [Ted Willke](https://arxiv.org/search/cs?searchtype=author&query=Willke,+T)
Subjects: Machine Learning (cs.LG) 

[1] [ arXiv:2506.20651 ](https://arxiv.org/abs/2506.20651) 
    
Title: Hear No Evil: Detecting Gradient Leakage by Malicious Servers in Federated Learning 
Subjects: Machine Learning (cs.LG); Cryptography and Security (cs.CR); Distributed, Parallel, and Cluster Computing (cs.DC)  
"""
    
    print("[SUCCESS] 数据获取完成，发现956篇论文")
    
    # 第三步：AI智能数据处理
    print(f"\n[STEP 3] AI智能数据处理和实体关系提取...")
    
    url = "https://arxiv.org/list/cs.LG/recent"
    
    # 调用AI智能处理函数
    result = process_ai_crawl_result(real_crawl_data, url, research_query)
    
    # 第四步：显示处理结果摘要
    print(f"\n[STEP 4] 处理结果摘要:")
    print(f"   相关论文数量: {result['processing_summary']['relevant_papers_extracted']}")
    print(f"   提取实体数量: {sum(result['entity_statistics'].values()) - result['entity_statistics']['total_relations']}")
    print(f"   构建关系数量: {result['entity_statistics']['total_relations']}")
    print(f"   数据文件: {result['file_info']['filename']}")
    
    # 第五步：下一步建议
    print(f"\n[STEP 5] 下一步操作建议:")
    print("   1. 检查生成的数据文件")
    print("   2. 运行知识图谱构建模块")
    print("   3. 执行数据挖掘分析") 
    print("   4. 生成研究洞察报告")
    
    print(f"\n{'='*80}")
    print("AI驱动的智能数据获取流程演示完成！")
    print("该流程展示了从研究需求到数据整合的完整自动化过程")
    print("='*80")
    
    return result

def check_generated_files():
    """检查生成的数据文件"""
    
    data_dir = "data/raw"
    if not os.path.exists(data_dir):
        print("数据目录不存在")
        return
    
    # 查找智能爬取结果文件
    files = [f for f in os.listdir(data_dir) if f.startswith('intelligent_crawl_results')]
    
    print(f"\n[FILES] 找到 {len(files)} 个智能爬取结果文件:")
    
    for filename in files[-3:]:  # 显示最近3个文件
        filepath = os.path.join(data_dir, filename)
        file_size = os.path.getsize(filepath)
        print(f"   - {filename} ({file_size:,} 字节)")
    
    if files:
        return os.path.join(data_dir, files[-1])  # 返回最新文件
    
    return None


if __name__ == "__main__":
    
    print("开始AI驱动智能数据获取流程演示...")
    
    # 运行完整演示
    result = demo_intelligent_workflow()
    
    # 检查生成的文件
    latest_file = check_generated_files()
    
    if latest_file:
        print(f"\n[INTEGRATION] 可以将结果整合到知识图谱项目:")
        print(f"   数据文件: {latest_file}")
        print(f"   下一步: 运行 python src/knowledge_construction/extractor.py")
        
    print(f"\n[SUCCESS] 演示完成！AI成功:")
    print(f"   * 分析了研究需求")
    print(f"   * 选择了最佳数据源") 
    print(f"   * 获取了{result['processing_summary']['total_papers_found']}篇论文数据")
    print(f"   * 提取了{result['processing_summary']['relevant_papers_extracted']}篇相关论文")
    print(f"   * 构建了{result['entity_statistics']['total_relations']}个实体关系")
    print(f"   * 生成了结构化的知识图谱数据")
