"""
知识图谱构建模块 - 实体关系提取器
不使用LLM，基于ArXiv结构化数据和传统NLP方法提取实体关系
"""

import json
import re
import os
from typing import List, Dict, Tuple, Set
from datetime import datetime
import spacy
from collections import defaultdict


class EntityRelationExtractor:
    """实体关系提取器"""
    
    def __init__(self):
        # 加载spaCy模型用于NLP处理
        try:
            self.nlp = spacy.load("en_core_web_sm")
        except OSError:
            print("警告: 未找到spaCy英文模型，请安装: python -m spacy download en_core_web_sm")
            self.nlp = None
        
        # 定义实体类型
        self.entity_types = {
            "Paper": set(),
            "Author": set(), 
            "Topic": set(),
            "Subject": set(),
            "Institution": set()
        }
        
        # 存储关系
        self.relations = []
        
        # 主题关键词库（用于从摘要中提取主题）
        self.topic_keywords = {
            "machine_learning": ["machine learning", "neural network", "deep learning", "classification", "regression"],
            "natural_language_processing": ["nlp", "natural language", "text processing", "language model"],
            "computer_vision": ["computer vision", "image", "visual", "object detection", "segmentation"],
            "artificial_intelligence": ["artificial intelligence", "ai", "intelligent", "automation"],
            "graph_neural_network": ["graph neural", "gnn", "graph convolution", "node classification"],
            "reinforcement_learning": ["reinforcement learning", "rl", "agent", "policy", "reward"],
            "blockchain": ["blockchain", "bitcoin", "cryptocurrency", "distributed ledger"],
            "robotics": ["robot", "robotics", "autonomous", "navigation", "manipulation"]
        }
    
    def extract_from_paper(self, paper_data: Dict) -> Dict:
        """从单篇论文数据中提取实体和关系"""
        entities = defaultdict(set)
        relations = []
        
        # 提取Paper实体
        paper_id = paper_data['arxiv_id']
        paper_entity = {
            "id": paper_id,
            "type": "Paper",
            "properties": {
                "title": paper_data.get('title', ''),
                "abstract": paper_data.get('abstract', ''),
                "arxiv_id": paper_id,
                "published_date": paper_data.get('submitted_date', ''),
                "url": paper_data.get('url', '')
            }
        }
        entities["Paper"].add(paper_id)
        
        # 提取Author实体和AUTHORED_BY关系
        for author_name in paper_data.get('authors', []):
            author_id = self._normalize_author_name(author_name)
            entities["Author"].add(author_id)
            
            # 创建AUTHORED_BY关系
            relations.append({
                "type": "AUTHORED_BY",
                "source": paper_id,
                "target": author_id,
                "source_type": "Paper",
                "target_type": "Author"
            })
        
        # 提取Subject实体和BELONGS_TO关系
        for subject in paper_data.get('subjects', []):
            subject_id = subject['code']
            entities["Subject"].add(subject_id)
            
            # 创建BELONGS_TO关系
            relations.append({
                "type": "BELONGS_TO", 
                "source": paper_id,
                "target": subject_id,
                "source_type": "Paper",
                "target_type": "Subject"
            })
        
        # 从摘要中提取Topic实体和HAS_TOPIC关系
        topics = self._extract_topics_from_abstract(paper_data.get('abstract', ''))
        for topic in topics:
            topic_id = topic.lower().replace(' ', '_')
            entities["Topic"].add(topic_id)
            
            # 创建HAS_TOPIC关系
            relations.append({
                "type": "HAS_TOPIC",
                "source": paper_id,
                "target": topic_id,
                "source_type": "Paper", 
                "target_type": "Topic"
            })
        
        return {
            "entities": dict(entities),
            "relations": relations,
            "paper_entity": paper_entity
        }
    
    def _normalize_author_name(self, name: str) -> str:
        """标准化作者姓名"""
        # 移除多余空格，转换为标准格式
        normalized = re.sub(r'\s+', ' ', name.strip())
        # 转换为ID格式（小写，空格替换为下划线）
        return normalized.lower().replace(' ', '_')
    
    def _extract_topics_from_abstract(self, abstract: str) -> List[str]:
        """从摘要中提取主题"""
        if not abstract:
            return []
        
        abstract_lower = abstract.lower()
        found_topics = []
        
        # 基于关键词匹配提取主题
        for topic, keywords in self.topic_keywords.items():
            for keyword in keywords:
                if keyword in abstract_lower:
                    found_topics.append(topic)
                    break
        
        # 如果有spaCy模型，提取额外的技术术语
        if self.nlp:
            doc = self.nlp(abstract)
            # 提取名词短语作为潜在主题
            noun_phrases = [chunk.text.lower() for chunk in doc.noun_chunks 
                          if len(chunk.text.split()) <= 3 and len(chunk.text) > 5]
            
            # 过滤技术相关的名词短语
            tech_phrases = [phrase for phrase in noun_phrases 
                          if any(word in phrase for word in 
                                ['network', 'learning', 'model', 'algorithm', 'method', 'system'])]
            
            found_topics.extend(tech_phrases[:5])  # 最多添加5个
        
        return list(set(found_topics))  # 去重
    
    def process_all_papers(self, data_dir: str = "data/raw") -> Dict:
        """处理所有抓取的论文数据"""
        print("开始处理论文数据...")
        
        all_entities = defaultdict(set)
        all_relations = []
        paper_entities = []
        
        # 读取所有JSON文件
        json_files = [f for f in os.listdir(data_dir) if f.endswith('.json')]
        
        for filename in json_files:
            filepath = os.path.join(data_dir, filename)
            
            try:
                with open(filepath, 'r', encoding='utf-8') as f:
                    paper_data = json.load(f)
                
                # 提取实体关系
                extracted = self.extract_from_paper(paper_data)
                
                # 合并实体
                for entity_type, entities in extracted["entities"].items():
                    all_entities[entity_type].update(entities)
                
                # 合并关系
                all_relations.extend(extracted["relations"])
                paper_entities.append(extracted["paper_entity"])
                
                print(f"处理完成: {paper_data.get('title', filename)[:50]}...")
                
            except Exception as e:
                print(f"处理文件 {filename} 时出错: {e}")
        
        # 生成作者合作关系
        coauthor_relations = self._generate_coauthor_relations(all_relations)
        all_relations.extend(coauthor_relations)
        
        result = {
            "entities": {k: list(v) for k, v in all_entities.items()},
            "relations": all_relations,
            "paper_entities": paper_entities,
            "statistics": {
                "total_papers": len(paper_entities),
                "total_authors": len(all_entities["Author"]),
                "total_topics": len(all_entities["Topic"]),
                "total_subjects": len(all_entities["Subject"]),
                "total_relations": len(all_relations)
            }
        }
        
        return result
    
    def _generate_coauthor_relations(self, relations: List[Dict]) -> List[Dict]:
        """生成作者合作关系"""
        # 按论文分组作者
        paper_authors = defaultdict(list)
        for rel in relations:
            if rel["type"] == "AUTHORED_BY":
                paper_authors[rel["source"]].append(rel["target"])
        
        coauthor_relations = []
        # 为每篇有多个作者的论文生成合作关系
        for paper_id, authors in paper_authors.items():
            if len(authors) > 1:
                for i in range(len(authors)):
                    for j in range(i+1, len(authors)):
                        coauthor_relations.append({
                            "type": "COLLABORATES_WITH",
                            "source": authors[i],
                            "target": authors[j],
                            "source_type": "Author",
                            "target_type": "Author",
                            "via_paper": paper_id
                        })
        
        return coauthor_relations
    
    def save_extracted_data(self, extracted_data: Dict, output_dir: str = "data/processed"):
        """保存提取的实体关系数据"""
        os.makedirs(output_dir, exist_ok=True)
        
        # 保存完整的提取结果
        with open(os.path.join(output_dir, "extracted_entities_relations.json"), 'w', encoding='utf-8') as f:
            json.dump(extracted_data, f, ensure_ascii=False, indent=2)
        
        # 分别保存实体和关系文件
        with open(os.path.join(output_dir, "entities.json"), 'w', encoding='utf-8') as f:
            json.dump(extracted_data["entities"], f, ensure_ascii=False, indent=2)
        
        with open(os.path.join(output_dir, "relations.json"), 'w', encoding='utf-8') as f:
            json.dump(extracted_data["relations"], f, ensure_ascii=False, indent=2)
        
        # 保存统计信息
        with open(os.path.join(output_dir, "statistics.json"), 'w', encoding='utf-8') as f:
            json.dump(extracted_data["statistics"], f, ensure_ascii=False, indent=2)
        
        print(f"数据保存完成:")
        print(f"  实体文件: {output_dir}/entities.json")
        print(f"  关系文件: {output_dir}/relations.json") 
        print(f"  统计文件: {output_dir}/statistics.json")
        
        return output_dir


if __name__ == "__main__":
    extractor = EntityRelationExtractor()
    
    # 处理所有论文数据
    extracted_data = extractor.process_all_papers()
    
    # 显示统计信息
    stats = extracted_data["statistics"]
    print(f"\n=== 实体关系提取完成 ===")
    print(f"论文数量: {stats['total_papers']}")
    print(f"作者数量: {stats['total_authors']}")
    print(f"主题数量: {stats['total_topics']}")
    print(f"学科数量: {stats['total_subjects']}")
    print(f"关系数量: {stats['total_relations']}")
    
    # 保存数据
    output_dir = extractor.save_extracted_data(extracted_data)
