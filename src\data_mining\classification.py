"""
分类预测模块 - 图神经网络
基于PyTorch Geometric实现GraphSAGE模型进行节点分类
"""

import json
import os
import sys
from pathlib import Path
import numpy as np
import networkx as nx
from collections import defaultdict
import matplotlib.pyplot as plt

# 设置项目路径
PROJECT_ROOT = Path(__file__).parent.parent.parent.absolute()
sys.path.insert(0, str(PROJECT_ROOT))

# 导入字体配置模块
try:
    from src.utils.font_config import setup_chinese_font
    setup_chinese_font()  # 确保字体配置生效
except ImportError:
    print("警告: 无法导入字体配置模块，中文可能显示异常")

try:
    import torch
    import torch.nn.functional as F
    from torch_geometric.data import Data
    from torch_geometric.nn import SAGEConv
    from sklearn.model_selection import train_test_split
    from sklearn.metrics import accuracy_score, classification_report
    from sklearn.preprocessing import LabelEncoder
    TORCH_AVAILABLE = True
except ImportError:
    print("警告: PyTorch Geometric或sklearn未安装，将使用简化的分类方法")
    TORCH_AVAILABLE = False


class GraphSAGE(torch.nn.Module):
    """GraphSAGE模型"""
    
    def __init__(self, input_dim, hidden_dim, output_dim, num_layers=2):
        super(GraphSAGE, self).__init__()
        self.num_layers = num_layers
        
        self.convs = torch.nn.ModuleList()
        self.convs.append(SAGEConv(input_dim, hidden_dim))
        
        for _ in range(num_layers - 2):
            self.convs.append(SAGEConv(hidden_dim, hidden_dim))
        
        self.convs.append(SAGEConv(hidden_dim, output_dim))
        self.dropout = torch.nn.Dropout(0.5)
        
    def forward(self, x, edge_index):
        for i, conv in enumerate(self.convs[:-1]):
            x = conv(x, edge_index)
            x = F.relu(x)
            x = self.dropout(x)
        
        x = self.convs[-1](x, edge_index)
        return F.log_softmax(x, dim=1)


class GraphClassifier:
    """图节点分类器"""
    
    def __init__(self, data_file: str = None):
        self.data_file = data_file or str(PROJECT_ROOT / "data" / "processed" / "extracted_entities_relations.json")
        self.graph = nx.Graph()
        self.entities = {}
        self.relations = []
        self.node_features = {}
        self.node_labels = {}
        self.model = None
        self.label_encoder = None
        
    def load_data(self):
        """加载处理后的数据"""
        print("加载数据...")
        
        if not os.path.exists(self.data_file):
            print(f"数据文件不存在: {self.data_file}")
            return False
        
        with open(self.data_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        self.entities = data.get("entities", {})
        self.relations = data.get("relations", [])
        
        print(f"加载完成: {len(self.relations)} 个关系")
        return True
        
    def build_heterogeneous_graph(self):
        """构建包含论文、作者、主题的异构图"""
        print("构建异构图...")
        
        # 添加所有节点
        all_nodes = []
        node_types = {}
        
        # 添加论文节点
        papers = self.entities.get("Paper", [])
        for paper_id in papers:
            all_nodes.append(paper_id)
            node_types[paper_id] = "Paper"
        
        # 添加作者节点
        authors = self.entities.get("Author", [])
        for author in authors:
            all_nodes.append(author)
            node_types[author] = "Author"
        
        # 添加主题节点
        topics = self.entities.get("Topic", [])
        for topic in topics:
            all_nodes.append(topic)
            node_types[topic] = "Topic"
        
        # 添加学科节点
        subjects = self.entities.get("Subject", [])
        for subject in subjects:
            all_nodes.append(subject)
            node_types[subject] = "Subject"
        
        # 将节点添加到图中
        for node in all_nodes:
            self.graph.add_node(node, node_type=node_types[node])
        
        # 添加边
        edge_count = 0
        for relation in self.relations:
            source = relation["source"]
            target = relation["target"]
            
            if source in all_nodes and target in all_nodes:
                self.graph.add_edge(source, target, 
                                  relation_type=relation["type"])
                edge_count += 1
        
        print(f"异构图构建完成: {len(all_nodes)} 个节点, {edge_count} 条边")
        print(f"节点类型分布: Papers={len(papers)}, Authors={len(authors)}, Topics={len(topics)}, Subjects={len(subjects)}")
        
        return len(all_nodes) > 0
        
    def create_node_features(self):
        """创建节点特征"""
        print("创建节点特征...")
        
        # 为每个节点创建特征向量
        for node in self.graph.nodes():
            node_data = self.graph.nodes[node]
            node_type = node_data.get("node_type", "Unknown")
            
            # 基本特征：节点类型的one-hot编码
            type_features = [0, 0, 0, 0]  # [Paper, Author, Topic, Subject]
            if node_type == "Paper":
                type_features[0] = 1
            elif node_type == "Author":
                type_features[1] = 1  
            elif node_type == "Topic":
                type_features[2] = 1
            elif node_type == "Subject":
                type_features[3] = 1
            
            # 图结构特征
            degree = self.graph.degree(node)
            clustering_coeff = nx.clustering(self.graph, node)
            
            # 邻居类型统计
            neighbor_types = defaultdict(int)
            for neighbor in self.graph.neighbors(node):
                neighbor_type = self.graph.nodes[neighbor].get("node_type", "Unknown")
                neighbor_types[neighbor_type] += 1
            
            neighbor_features = [
                neighbor_types.get("Paper", 0),
                neighbor_types.get("Author", 0),
                neighbor_types.get("Topic", 0),
                neighbor_types.get("Subject", 0)
            ]
            
            # 组合所有特征
            features = type_features + [degree, clustering_coeff] + neighbor_features
            self.node_features[node] = features
            
            # 设置标签（我们预测节点类型）
            self.node_labels[node] = node_type
        
        print(f"特征创建完成: {len(self.node_features)} 个节点, 特征维度: {len(features)}")
        return True
        
    def prepare_pytorch_data(self):
        """准备PyTorch Geometric数据"""
        if not TORCH_AVAILABLE:
            return None
            
        print("准备PyTorch数据...")
        
        # 节点ID映射
        node_list = list(self.graph.nodes())
        node_to_idx = {node: idx for idx, node in enumerate(node_list)}
        
        # 特征矩阵
        features = []
        labels = []
        
        for node in node_list:
            features.append(self.node_features[node])
            labels.append(self.node_labels[node])
        
        # 标签编码
        self.label_encoder = LabelEncoder()
        encoded_labels = self.label_encoder.fit_transform(labels)
        
        # 边索引
        edge_index = []
        for edge in self.graph.edges():
            source_idx = node_to_idx[edge[0]]
            target_idx = node_to_idx[edge[1]]
            edge_index.append([source_idx, target_idx])
            edge_index.append([target_idx, source_idx])  # 无向图
        
        # 转换为张量
        x = torch.FloatTensor(features)
        y = torch.LongTensor(encoded_labels)
        edge_index = torch.LongTensor(edge_index).t().contiguous()
        
        # 创建数据对象
        data = Data(x=x, edge_index=edge_index, y=y)
        
        print(f"PyTorch数据准备完成: {data.x.shape[0]} 个节点, {data.x.shape[1]} 个特征")
        return data
        
    def train_graphsage_model(self, data):
        """训练GraphSAGE模型"""
        if not TORCH_AVAILABLE:
            print("PyTorch不可用，跳过训练")
            return None
            
        print("训练GraphSAGE模型...")
        
        # 模型参数
        input_dim = data.x.shape[1]
        hidden_dim = 64
        output_dim = len(self.label_encoder.classes_)
        
        # 创建模型
        self.model = GraphSAGE(input_dim, hidden_dim, output_dim, num_layers=2)
        optimizer = torch.optim.Adam(self.model.parameters(), lr=0.01, weight_decay=5e-4)
        
        # 划分训练集和测试集（这里简单地用前80%作为训练集）
        num_nodes = data.x.shape[0]
        train_size = int(0.8 * num_nodes)
        
        train_mask = torch.zeros(num_nodes, dtype=torch.bool)
        train_mask[:train_size] = True
        test_mask = ~train_mask
        
        # 训练
        self.model.train()
        for epoch in range(200):
            optimizer.zero_grad()
            out = self.model(data.x, data.edge_index)
            loss = F.nll_loss(out[train_mask], data.y[train_mask])
            loss.backward()
            optimizer.step()
            
            if epoch % 50 == 0:
                print(f"Epoch {epoch}, Loss: {loss:.4f}")
        
        # 评估
        self.model.eval()
        with torch.no_grad():
            out = self.model(data.x, data.edge_index)
            pred = out.argmax(dim=1)
            
            train_acc = accuracy_score(data.y[train_mask].cpu(), pred[train_mask].cpu())
            test_acc = accuracy_score(data.y[test_mask].cpu(), pred[test_mask].cpu())
            
            print(f"训练准确率: {train_acc:.4f}")
            print(f"测试准确率: {test_acc:.4f}")
            
            # 详细分类报告
            test_labels = data.y[test_mask].cpu().numpy()
            test_pred = pred[test_mask].cpu().numpy()
            
            print("\n分类报告:")
            class_names = self.label_encoder.classes_
            print(classification_report(test_labels, test_pred, 
                                      target_names=class_names, zero_division=0))
        
        return {
            "train_accuracy": train_acc,
            "test_accuracy": test_acc,
            "predictions": pred.cpu().numpy(),
            "true_labels": data.y.cpu().numpy()
        }
        
    def simple_baseline_classification(self):
        """简单的基线分类方法（基于度中心性）"""
        print("运行简单基线分类...")
        
        # 计算每个节点的度中心性
        degree_centrality = nx.degree_centrality(self.graph)
        betweenness_centrality = nx.betweenness_centrality(self.graph)
        closeness_centrality = nx.closeness_centrality(self.graph)
        
        # 基于中心性特征进行简单预测
        predictions = {}
        true_labels = {}
        
        for node in self.graph.nodes():
            true_label = self.node_labels[node]
            true_labels[node] = true_label
            
            # 简单规则：基于度中心性预测
            degree = degree_centrality[node]
            
            if degree > 0.5:
                pred_label = "Paper"  # 高度节点可能是论文
            elif degree > 0.3:
                pred_label = "Author"  # 中等度节点可能是作者
            elif degree > 0.1:
                pred_label = "Topic"  # 低度节点可能是主题
            else:
                pred_label = "Subject"  # 最低度节点可能是学科
            
            predictions[node] = pred_label
        
        # 计算准确率
        correct = sum(1 for node in true_labels if true_labels[node] == predictions[node])
        accuracy = correct / len(true_labels)
        
        print(f"基线分类准确率: {accuracy:.4f}")
        
        # 按类型分析
        type_stats = defaultdict(lambda: {"correct": 0, "total": 0})
        for node in true_labels:
            true_type = true_labels[node]
            pred_type = predictions[node]
            
            type_stats[true_type]["total"] += 1
            if true_type == pred_type:
                type_stats[true_type]["correct"] += 1
        
        print("\n各类型准确率:")
        for node_type, stats in type_stats.items():
            type_acc = stats["correct"] / stats["total"] if stats["total"] > 0 else 0
            print(f"  {node_type}: {type_acc:.4f} ({stats['correct']}/{stats['total']})")
        
        return {
            "accuracy": accuracy,
            "predictions": predictions,
            "true_labels": true_labels,
            "type_stats": dict(type_stats)
        }
        
    def visualize_classification_results(self, results, output_file: str = None):
        """可视化分类结果"""
        print("生成分类结果可视化...")
        
        # 设置图形大小
        plt.figure(figsize=(12, 8))
        
        # 使用spring布局
        pos = nx.spring_layout(self.graph, k=1, iterations=50)
        
        # 为不同类型分配颜色
        type_colors = {
            "Paper": "red",
            "Author": "blue", 
            "Topic": "green",
            "Subject": "orange"
        }
        
        # 绘制节点，根据预测结果着色
        for node_type, color in type_colors.items():
            # 真实标签为该类型的节点
            true_nodes = [node for node in self.graph.nodes() 
                         if self.node_labels[node] == node_type]
            
            if true_nodes:
                nx.draw_networkx_nodes(
                    self.graph, pos,
                    nodelist=true_nodes,
                    node_color=color,
                    node_size=300,
                    alpha=0.7,
                    label=f'{node_type} (真实)'
                )
        
        # 绘制边
        nx.draw_networkx_edges(self.graph, pos, alpha=0.3, width=0.5)
        
        plt.title("Node Classification Results")
        plt.legend()
        plt.axis('off')
        plt.tight_layout()
        
        # 保存图像
        if not output_file:
            output_file = str(PROJECT_ROOT / "data" / "graphs" / "classification_result.png")
        
        os.makedirs(os.path.dirname(output_file), exist_ok=True)
        plt.savefig(output_file, dpi=300, bbox_inches='tight')
        print(f"分类结果可视化保存到: {output_file}")
        
        plt.show()
        
    def save_classification_results(self, results, output_file: str = None):
        """保存分类结果"""
        if not output_file:
            output_file = str(PROJECT_ROOT / "data" / "processed" / "classification_results.json")
        
        save_data = {
            "method": "GraphSAGE" if TORCH_AVAILABLE else "Baseline",
            "accuracy": results.get("accuracy", results.get("test_accuracy", 0)),
            "results_summary": results,
            "node_features_dim": len(next(iter(self.node_features.values()))),
            "graph_stats": {
                "num_nodes": len(self.graph.nodes()),
                "num_edges": len(self.graph.edges()),
                "num_classes": len(set(self.node_labels.values()))
            },
            "generated_at": __import__('datetime').datetime.now().isoformat()
        }
        
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(save_data, f, ensure_ascii=False, indent=2)
        
        print(f"分类结果保存到: {output_file}")
        return output_file


def run_node_classification():
    """运行节点分类任务"""
    print("=== 图节点分类任务 ===")
    
    classifier = GraphClassifier()
    
    # 加载数据
    if not classifier.load_data():
        return None
    
    # 构建异构图
    if not classifier.build_heterogeneous_graph():
        print("无法构建异构图")
        return None
    
    # 创建节点特征
    if not classifier.create_node_features():
        print("无法创建节点特征")
        return None
    
    # 尝试训练GraphSAGE模型
    if TORCH_AVAILABLE:
        print("\n--- 使用GraphSAGE模型 ---")
        data = classifier.prepare_pytorch_data()
        if data is not None:
            results = classifier.train_graphsage_model(data)
        else:
            results = None
    else:
        results = None
    
    # 如果GraphSAGE不可用，使用基线方法
    if results is None:
        print("\n--- 使用基线分类方法 ---")
        results = classifier.simple_baseline_classification()
    
    # 可视化和保存结果
    try:
        classifier.visualize_classification_results(results)
    except Exception as e:
        print(f"可视化失败: {e}")
    
    output_file = classifier.save_classification_results(results)
    
    return classifier


if __name__ == "__main__":
    # 运行节点分类
    classifier = run_node_classification()
    
    print("\n=== 分类任务完成 ===")
    print("结果文件: classification_results.json")
    print("可视化文件: classification_result.png")
