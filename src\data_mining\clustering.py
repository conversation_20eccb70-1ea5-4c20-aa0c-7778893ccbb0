"""
聚类分析模块 - 社区发现
基于NetworkX和python-louvain进行图聚类分析
"""

import json
import os
import sys
from pathlib import Path
import networkx as nx
import matplotlib.pyplot as plt
from collections import defaultdict

# 设置项目路径
PROJECT_ROOT = Path(__file__).parent.parent.parent.absolute()
sys.path.insert(0, str(PROJECT_ROOT))

# 导入字体配置模块
try:
    from src.utils.font_config import setup_chinese_font
    setup_chinese_font()  # 确保字体配置生效
except ImportError:
    print("警告: 无法导入字体配置模块，中文可能显示异常")

try:
    import community as community_louvain
    LOUVAIN_AVAILABLE = True
except ImportError:
    print("警告: python-louvain未安装，将使用简化的聚类方法")
    LOUVAIN_AVAILABLE = False


class GraphClusteringAnalyzer:
    """图聚类分析器"""
    
    def __init__(self, data_file: str = None):
        self.data_file = data_file or str(PROJECT_ROOT / "data" / "processed" / "extracted_entities_relations.json")
        self.graph = nx.Graph()
        self.entities = {}
        self.relations = []
        self.clusters = {}
        
    def load_data(self):
        """加载处理后的数据"""
        print("加载数据...")
        
        if not os.path.exists(self.data_file):
            print(f"数据文件不存在: {self.data_file}")
            return False
        
        with open(self.data_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        self.entities = data.get("entities", {})
        self.relations = data.get("relations", [])
        
        print(f"加载完成: {len(self.relations)} 个关系")
        return True
        
    def build_collaboration_graph(self):
        """构建作者合作网络图"""
        print("构建作者合作网络...")
        
        # 添加作者节点
        authors = self.entities.get("Author", [])
        for author in authors:
            # 清理作者名称，用于显示
            display_name = author.replace('_', ' ').title()
            self.graph.add_node(author, type='Author', display_name=display_name)
        
        # 添加合作关系边
        collaboration_count = 0
        for relation in self.relations:
            if relation["type"] == "COLLABORATES_WITH":
                source = relation["source"]
                target = relation["target"]
                
                # 如果边已存在，增加权重
                if self.graph.has_edge(source, target):
                    self.graph[source][target]['weight'] += 1
                else:
                    self.graph.add_edge(source, target, weight=1, relation_type='collaboration')
                    collaboration_count += 1
        
        print(f"作者网络构建完成: {len(authors)} 个节点, {collaboration_count} 条合作边")
        return len(authors) > 0
        
    def build_paper_topic_graph(self):
        """构建论文-主题网络图"""
        print("构建论文-主题网络...")
        
        # 清空图
        self.graph.clear()
        
        # 添加论文节点
        papers = self.entities.get("Paper", [])  # 这里实际是paper实体的列表
        for paper_id in papers:
            self.graph.add_node(paper_id, type='Paper')
        
        # 添加主题节点
        topics = self.entities.get("Topic", [])
        for topic in topics:
            display_name = topic.replace('_', ' ').title()
            self.graph.add_node(topic, type='Topic', display_name=display_name)
        
        # 添加论文-主题关系边
        topic_edges = 0
        for relation in self.relations:
            if relation["type"] == "HAS_TOPIC":
                source = relation["source"]  # 论文ID
                target = relation["target"]  # 主题ID
                
                if source in papers and target in topics:
                    self.graph.add_edge(source, target, relation_type='has_topic')
                    topic_edges += 1
        
        print(f"论文-主题网络构建完成: {len(papers) + len(topics)} 个节点, {topic_edges} 条边")
        return len(papers) > 0 and len(topics) > 0
        
    def perform_clustering(self, method="louvain"):
        """执行聚类分析"""
        print(f"执行聚类分析 (方法: {method})...")
        
        if len(self.graph.nodes()) == 0:
            print("图为空，无法进行聚类")
            return {}
        
        if method == "louvain" and LOUVAIN_AVAILABLE:
            try:
                # 使用Louvain算法
                partition = community_louvain.best_partition(self.graph)
                self.clusters = partition
                
                # 计算模块度
                modularity = community_louvain.modularity(partition, self.graph)
                print(f"Louvain聚类完成，模块度: {modularity:.3f}")
                
            except Exception as e:
                print(f"Louvain聚类失败: {e}")
                method = "connected_components"
        
        if method == "connected_components" or not LOUVAIN_AVAILABLE:
            # 使用连通分量作为简单聚类
            components = nx.connected_components(self.graph)
            partition = {}
            for i, component in enumerate(components):
                for node in component:
                    partition[node] = i
            
            self.clusters = partition
            print(f"连通分量聚类完成，发现 {len(set(partition.values()))} 个聚类")
        
        return self.clusters
        
    def analyze_clusters(self):
        """分析聚类结果"""
        if not self.clusters:
            print("没有聚类结果可分析")
            return {}
        
        # 按聚类ID分组
        clusters_by_id = defaultdict(list)
        for node, cluster_id in self.clusters.items():
            clusters_by_id[cluster_id].append(node)
        
        print(f"\n=== 聚类分析结果 ===")
        print(f"总聚类数: {len(clusters_by_id)}")
        
        cluster_analysis = {}
        
        for cluster_id, nodes in clusters_by_id.items():
            # 分析每个聚类的组成
            node_types = defaultdict(int)
            node_details = []
            
            for node in nodes:
                node_data = self.graph.nodes[node]
                node_type = node_data.get('type', 'Unknown')
                node_types[node_type] += 1
                
                display_name = node_data.get('display_name', node)
                node_details.append({
                    'id': node,
                    'type': node_type,
                    'display_name': display_name
                })
            
            cluster_analysis[cluster_id] = {
                'size': len(nodes),
                'composition': dict(node_types),
                'nodes': node_details
            }
            
            print(f"\n聚类 {cluster_id} (大小: {len(nodes)}):")
            for node_type, count in node_types.items():
                print(f"  - {node_type}: {count} 个")
            
            # 显示节点详情
            if len(nodes) <= 10:  # 只显示小聚类的详情
                for detail in node_details:
                    print(f"    * {detail['display_name']} ({detail['type']})")
        
        return cluster_analysis
        
    def visualize_clusters(self, output_file: str = None):
        """可视化聚类结果"""
        if not self.clusters or len(self.graph.nodes()) == 0:
            print("没有数据可可视化")
            return
        
        print("生成聚类可视化...")
        
        # 设置图形大小
        plt.figure(figsize=(12, 8))
        
        # 使用spring布局
        pos = nx.spring_layout(self.graph, k=1, iterations=50)
        
        # 为不同聚类分配颜色
        cluster_ids = list(set(self.clusters.values()))
        colors = plt.cm.Set3(range(len(cluster_ids)))
        
        # 绘制节点
        for cluster_id in cluster_ids:
            cluster_nodes = [node for node, cid in self.clusters.items() if cid == cluster_id]
            nx.draw_networkx_nodes(
                self.graph, pos, 
                nodelist=cluster_nodes,
                node_color=[colors[cluster_id]],
                node_size=300,
                alpha=0.8,
                label=f'聚类 {cluster_id}'
            )
        
        # 绘制边
        nx.draw_networkx_edges(self.graph, pos, alpha=0.5, width=0.5)
        
        # 添加标签
        labels = {}
        for node in self.graph.nodes():
            node_data = self.graph.nodes[node]
            display_name = node_data.get('display_name', node)
            # 缩短标签以便显示
            if len(display_name) > 15:
                labels[node] = display_name[:12] + "..."
            else:
                labels[node] = display_name
        
        nx.draw_networkx_labels(self.graph, pos, labels, font_size=8)
        
        plt.title("知识图谱聚类结果")
        plt.legend()
        plt.axis('off')
        plt.tight_layout()
        
        # 保存图像
        if not output_file:
            output_file = str(PROJECT_ROOT / "data" / "graphs" / "clustering_result.png")
        
        os.makedirs(os.path.dirname(output_file), exist_ok=True)
        plt.savefig(output_file, dpi=300, bbox_inches='tight')
        print(f"聚类可视化保存到: {output_file}")
        
        plt.show()
        
    def save_clustering_results(self, output_file: str = None):
        """保存聚类结果"""
        if not output_file:
            output_file = str(PROJECT_ROOT / "data" / "processed" / "clustering_results.json")
        
        # 分析聚类
        cluster_analysis = self.analyze_clusters()
        
        results = {
            "clustering_method": "louvain" if LOUVAIN_AVAILABLE else "connected_components",
            "total_nodes": len(self.graph.nodes()),
            "total_edges": len(self.graph.edges()),
            "num_clusters": len(set(self.clusters.values())) if self.clusters else 0,
            "node_cluster_assignments": self.clusters,
            "cluster_analysis": cluster_analysis,
            "generated_at": __import__('datetime').datetime.now().isoformat()
        }
        
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(results, f, ensure_ascii=False, indent=2)
        
        print(f"聚类结果保存到: {output_file}")
        return output_file


def run_author_clustering():
    """运行作者合作网络聚类分析"""
    print("=== 作者合作网络聚类分析 ===")
    
    analyzer = GraphClusteringAnalyzer()
    
    # 加载数据
    if not analyzer.load_data():
        return None
    
    # 构建作者合作网络
    if not analyzer.build_collaboration_graph():
        print("无法构建作者合作网络")
        return None
    
    # 执行聚类
    clusters = analyzer.perform_clustering()
    
    # 分析和保存结果
    analyzer.analyze_clusters()
    output_file = analyzer.save_clustering_results(
        str(PROJECT_ROOT / "data" / "processed" / "author_clustering_results.json")
    )
    
    # 可视化
    try:
        analyzer.visualize_clusters(
            str(PROJECT_ROOT / "data" / "graphs" / "author_collaboration_clusters.png")
        )
    except Exception as e:
        print(f"可视化失败: {e}")
    
    return analyzer


def run_paper_topic_clustering():
    """运行论文-主题网络聚类分析"""
    print("\n=== 论文-主题网络聚类分析 ===")
    
    analyzer = GraphClusteringAnalyzer()
    
    # 加载数据
    if not analyzer.load_data():
        return None
    
    # 构建论文-主题网络
    if not analyzer.build_paper_topic_graph():
        print("无法构建论文-主题网络")
        return None
    
    # 执行聚类
    clusters = analyzer.perform_clustering()
    
    # 分析和保存结果
    analyzer.analyze_clusters()
    output_file = analyzer.save_clustering_results(
        str(PROJECT_ROOT / "data" / "processed" / "paper_topic_clustering_results.json")
    )
    
    # 可视化
    try:
        analyzer.visualize_clusters(
            str(PROJECT_ROOT / "data" / "graphs" / "paper_topic_clusters.png")
        )
    except Exception as e:
        print(f"可视化失败: {e}")
    
    return analyzer


if __name__ == "__main__":
    # 运行作者合作网络聚类
    author_analyzer = run_author_clustering()
    
    # 运行论文-主题网络聚类
    topic_analyzer = run_paper_topic_clustering()
    
    print("\n=== 聚类分析完成 ===")
    print("结果文件:")
    print("- author_clustering_results.json")
    print("- paper_topic_clustering_results.json")
    print("可视化文件:")
    print("- author_collaboration_clusters.png") 
    print("- paper_topic_clusters.png")
