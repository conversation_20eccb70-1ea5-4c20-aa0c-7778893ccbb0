#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os
sys.path.append(r'C:\dev\MCP\knowledge-graph-mining')

# 切换到正确的工作目录
os.chdir(r'C:\dev\MCP\knowledge-graph-mining')

from src.knowledge_construction.extractor import EntityRelationExtractor

def run_extractor():
    """运行实体关系提取器"""
    
    extractor = EntityRelationExtractor()
    
    # 使用绝对路径
    data_dir = r"C:\dev\MCP\knowledge-graph-mining\data\raw"
    
    print(f"处理数据目录: {data_dir}")
    
    # 检查目录中的文件
    import os
    json_files = [f for f in os.listdir(data_dir) if f.endswith('.json')]
    print(f"找到JSON文件: {json_files}")
    
    # 处理所有论文数据
    extracted_data = extractor.process_all_papers(data_dir)
    
    # 显示统计信息
    stats = extracted_data["statistics"]
    print(f"\n=== 实体关系提取完成 ===")
    print(f"论文数量: {stats['total_papers']}")
    print(f"作者数量: {stats['total_authors']}")
    print(f"主题数量: {stats['total_topics']}")
    print(f"学科数量: {stats['total_subjects']}")
    print(f"关系数量: {stats['total_relations']}")
    
    # 保存数据
    output_dir = extractor.save_extracted_data(extracted_data)
    
    return extracted_data

if __name__ == "__main__":
    try:
        result = run_extractor()
        print(f"\n实体关系提取成功完成！")
    except Exception as e:
        print(f"提取过程出错: {e}")
        import traceback
        traceback.print_exc()
