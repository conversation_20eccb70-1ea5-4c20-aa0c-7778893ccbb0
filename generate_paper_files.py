#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import json
import os
from datetime import datetime
from pathlib import Path

def generate_individual_paper_files():
    """生成单个论文文件，符合extractor.py的期望格式"""
    
    # 读取我们的机器学习论文数据
    crawl_data_path = r"C:\dev\MCP\knowledge-graph-mining\data\raw\machine_learning_papers_crawl.json"
    
    if not os.path.exists(crawl_data_path):
        print(f"错误: 找不到文件 {crawl_data_path}")
        return
    
    with open(crawl_data_path, 'r', encoding='utf-8') as f:
        crawl_data = json.load(f)
    
    papers = crawl_data.get("selected_papers", [])
    
    # 创建数据目录
    data_dir = Path(r"C:\dev\MCP\knowledge-graph-mining\data\raw")
    data_dir.mkdir(parents=True, exist_ok=True)
    
    print(f"开始生成单个论文文件...")
    print(f"论文数量: {len(papers)}")
    
    generated_files = []
    
    for i, paper in enumerate(papers):
        # 转换为extractor.py期望的格式
        paper_data = {
            "arxiv_id": paper.get("id", f"unknown_{i}"),
            "title": paper.get("title", ""),
            "authors": paper.get("authors", []),
            "subjects": [],  # 需要转换subjects格式
            "abstract": "",  # 我们没有摘要数据
            "submitted_date": "2025-06-26",  # 模拟提交日期
            "url": paper.get("url", f"https://arxiv.org/abs/{paper.get('id', '')}")
        }
        
        # 转换subjects格式为extractor.py期望的格式
        for subject in paper.get("subjects", []):
            # 从subject字符串中提取代码
            if "(" in subject and ")" in subject:
                code = subject.split("(")[1].split(")")[0]
                name = subject.split("(")[0].strip()
            else:
                code = subject.replace(" ", "_").lower()
                name = subject
            
            paper_data["subjects"].append({
                "code": code,
                "name": name
            })
        
        # 保存单个论文文件
        filename = f"{paper_data['arxiv_id']}.json"
        filepath = data_dir / filename
        
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(paper_data, f, ensure_ascii=False, indent=2)
        
        generated_files.append(filename)
        print(f"  生成文件: {filename}")
    
    print(f"\n成功生成 {len(generated_files)} 个论文文件")
    print(f"文件保存在: {data_dir}")
    
    return generated_files

if __name__ == "__main__":
    try:
        files = generate_individual_paper_files()
        print(f"\n文件生成完成！")
        print(f"现在可以运行实体提取器: python src/knowledge_construction/extractor.py")
    except Exception as e:
        print(f"生成文件出错: {e}")
        import traceback
        traceback.print_exc()
