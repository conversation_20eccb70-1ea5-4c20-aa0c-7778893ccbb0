"""
关联规则模块 - 频繁子图挖掘 (Frequent Subgraph Mining)
将传统关联规则概念扩展到图数据，发现频繁出现的子图模式
"""

import json
import os
import sys
from pathlib import Path
import networkx as nx
from collections import defaultdict, Counter
import matplotlib.pyplot as plt
from itertools import combinations

# 设置项目路径
PROJECT_ROOT = Path(__file__).parent.parent.parent.absolute()
sys.path.insert(0, str(PROJECT_ROOT))

# 导入字体配置模块
try:
    from src.utils.font_config import setup_chinese_font
    setup_chinese_font()  # 确保字体配置生效
except ImportError:
    print("警告: 无法导入字体配置模块，中文可能显示异常")

try:
    from gspan_mining import gSpan
    GSPAN_AVAILABLE = True
except ImportError:
    print("警告: gspan-mining未安装，将使用简化的子图挖掘方法")
    GSPAN_AVAILABLE = False


class FrequentSubgraphMiner:
    """频繁子图挖掘器"""
    
    def __init__(self, data_file: str = None):
        self.data_file = data_file or str(PROJECT_ROOT / "data" / "processed" / "extracted_entities_relations.json")
        self.main_graph = nx.Graph()
        self.entities = {}
        self.relations = []
        self.frequent_patterns = []
        self.subgraph_database = []
        
    def load_data(self):
        """加载处理后的数据"""
        print("加载数据...")
        
        if not os.path.exists(self.data_file):
            print(f"数据文件不存在: {self.data_file}")
            return False
        
        with open(self.data_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        self.entities = data.get("entities", {})
        self.relations = data.get("relations", [])
        
        print(f"加载完成: {len(self.relations)} 个关系")
        return True
        
    def build_main_graph(self):
        """构建主图"""
        print("构建主图...")
        
        # 添加所有节点
        all_nodes = []
        for entity_type, entities in self.entities.items():
            for entity in entities:
                all_nodes.append(entity)
                self.main_graph.add_node(entity, type=entity_type)
        
        # 添加所有边
        edge_count = 0
        for relation in self.relations:
            source = relation["source"]
            target = relation["target"]
            
            if source in all_nodes and target in all_nodes:
                self.main_graph.add_edge(source, target, 
                                       relation_type=relation["type"])
                edge_count += 1
        
        print(f"主图构建完成: {len(all_nodes)} 个节点, {edge_count} 条边")
        return len(all_nodes) > 0
        
    def extract_ego_networks(self, radius=1):
        """提取每个节点的ego网络作为子图事务"""
        print(f"提取ego网络 (半径={radius})...")
        
        self.subgraph_database = []
        
        for center_node in self.main_graph.nodes():
            # 获取以center_node为中心的ego网络
            ego_nodes = set([center_node])
            
            # 扩展到指定半径
            current_layer = {center_node}
            for _ in range(radius):
                next_layer = set()
                for node in current_layer:
                    next_layer.update(self.main_graph.neighbors(node))
                ego_nodes.update(next_layer)
                current_layer = next_layer - ego_nodes
                ego_nodes.update(current_layer)
            
            # 创建子图
            ego_subgraph = self.main_graph.subgraph(ego_nodes).copy()
            
            if len(ego_subgraph.nodes()) > 1:  # 至少包含2个节点
                self.subgraph_database.append({
                    'center': center_node,
                    'subgraph': ego_subgraph,
                    'nodes': list(ego_nodes),
                    'size': len(ego_nodes)
                })
        
        print(f"提取了 {len(self.subgraph_database)} 个ego网络")
        return len(self.subgraph_database) > 0
        
    def mine_frequent_node_patterns(self, min_support=2):
        """挖掘频繁节点类型模式"""
        print(f"挖掘频繁节点类型模式 (最小支持度={min_support})...")
        
        # 统计不同大小的节点类型组合
        pattern_counts = defaultdict(int)
        
        for subgraph_data in self.subgraph_database:
            subgraph = subgraph_data['subgraph']
            
            # 获取节点类型
            node_types = []
            for node in subgraph.nodes():
                node_type = subgraph.nodes[node].get('type', 'Unknown')
                node_types.append(node_type)
            
            # 生成不同大小的类型组合
            type_counter = Counter(node_types)
            
            # 单个类型模式
            for node_type, count in type_counter.items():
                pattern = (node_type,)
                pattern_counts[pattern] += 1
            
            # 两个类型的组合模式
            for type1, type2 in combinations(type_counter.keys(), 2):
                pattern = tuple(sorted([type1, type2]))
                pattern_counts[pattern] += 1
            
            # 三个类型的组合模式（如果存在）
            if len(type_counter) >= 3:
                for type1, type2, type3 in combinations(type_counter.keys(), 3):
                    pattern = tuple(sorted([type1, type2, type3]))
                    pattern_counts[pattern] += 1
        
        # 过滤频繁模式
        frequent_patterns = {pattern: count for pattern, count in pattern_counts.items() 
                           if count >= min_support}
        
        print(f"发现 {len(frequent_patterns)} 个频繁节点类型模式")
        
        # 显示频繁模式
        print("\n频繁节点类型模式:")
        for pattern, count in sorted(frequent_patterns.items(), key=lambda x: x[1], reverse=True):
            support = count / len(self.subgraph_database)
            print(f"  {pattern}: 支持度={count}, 相对支持度={support:.3f}")
        
        return frequent_patterns
        
    def mine_frequent_edge_patterns(self, min_support=2):
        """挖掘频繁边类型模式"""
        print(f"挖掘频繁边类型模式 (最小支持度={min_support})...")
        
        # 统计边的类型模式
        edge_pattern_counts = defaultdict(int)
        
        for subgraph_data in self.subgraph_database:
            subgraph = subgraph_data['subgraph']
            
            # 获取边的模式
            edge_patterns = set()
            
            for edge in subgraph.edges():
                source, target = edge
                source_type = subgraph.nodes[source].get('type', 'Unknown')
                target_type = subgraph.nodes[target].get('type', 'Unknown')
                relation_type = subgraph.edges[edge].get('relation_type', 'Unknown')
                
                # 创建边模式 (源类型, 关系类型, 目标类型)
                pattern = (source_type, relation_type, target_type)
                edge_patterns.add(pattern)
                
                # 也添加反向模式（因为是无向图）
                reverse_pattern = (target_type, relation_type, source_type)
                edge_patterns.add(reverse_pattern)
            
            # 统计每个子图中出现的边模式
            for pattern in edge_patterns:
                edge_pattern_counts[pattern] += 1
        
        # 过滤频繁边模式
        frequent_edge_patterns = {pattern: count for pattern, count in edge_pattern_counts.items() 
                                if count >= min_support}
        
        print(f"发现 {len(frequent_edge_patterns)} 个频繁边模式")
        
        # 显示频繁边模式
        print("\n频繁边模式:")
        for pattern, count in sorted(frequent_edge_patterns.items(), key=lambda x: x[1], reverse=True):
            support = count / len(self.subgraph_database)
            source_type, relation_type, target_type = pattern
            print(f"  {source_type} --[{relation_type}]--> {target_type}: 支持度={count}, 相对支持度={support:.3f}")
        
        return frequent_edge_patterns
        
    def mine_frequent_triangles(self, min_support=2):
        """挖掘频繁三角形模式"""
        print(f"挖掘频繁三角形模式 (最小支持度={min_support})...")
        
        triangle_pattern_counts = defaultdict(int)
        
        for subgraph_data in self.subgraph_database:
            subgraph = subgraph_data['subgraph']
            
            # 找到所有三角形
            triangles = []
            for node in subgraph.nodes():
                neighbors = list(subgraph.neighbors(node))
                for i in range(len(neighbors)):
                    for j in range(i+1, len(neighbors)):
                        neighbor1, neighbor2 = neighbors[i], neighbors[j]
                        if subgraph.has_edge(neighbor1, neighbor2):
                            # 找到一个三角形
                            triangle = tuple(sorted([node, neighbor1, neighbor2]))
                            triangles.append(triangle)
            
            # 统计三角形的类型模式
            triangle_patterns = set()
            for triangle in triangles:
                node_types = []
                for node in triangle:
                    node_type = subgraph.nodes[node].get('type', 'Unknown')
                    node_types.append(node_type)
                
                # 创建三角形模式（排序后的节点类型）
                pattern = tuple(sorted(node_types))
                triangle_patterns.add(pattern)
            
            # 统计模式
            for pattern in triangle_patterns:
                triangle_pattern_counts[pattern] += 1
        
        # 过滤频繁三角形模式
        frequent_triangles = {pattern: count for pattern, count in triangle_pattern_counts.items() 
                            if count >= min_support}
        
        print(f"发现 {len(frequent_triangles)} 个频繁三角形模式")
        
        # 显示频繁三角形模式
        print("\n频繁三角形模式:")
        for pattern, count in sorted(frequent_triangles.items(), key=lambda x: x[1], reverse=True):
            support = count / len(self.subgraph_database)
            print(f"  {pattern}: 支持度={count}, 相对支持度={support:.3f}")
        
        return frequent_triangles
        
    def generate_association_rules(self, frequent_patterns, min_confidence=0.5):
        """从频繁模式生成关联规则"""
        print(f"生成关联规则 (最小置信度={min_confidence})...")
        
        association_rules = []
        
        # 对于每个频繁模式，尝试生成规则
        for pattern, support in frequent_patterns.items():
            if len(pattern) < 2:
                continue
                
            # 生成所有可能的前件->后件规则
            for i in range(1, len(pattern)):
                for antecedent_indices in combinations(range(len(pattern)), i):
                    antecedent = tuple(pattern[j] for j in antecedent_indices)
                    consequent_indices = [j for j in range(len(pattern)) if j not in antecedent_indices]
                    consequent = tuple(pattern[j] for j in consequent_indices)
                    
                    # 计算置信度
                    # 需要计算antecedent在数据库中的支持度
                    antecedent_support = 0
                    for subgraph_data in self.subgraph_database:
                        subgraph = subgraph_data['subgraph']
                        node_types = [subgraph.nodes[node].get('type', 'Unknown') 
                                    for node in subgraph.nodes()]
                        type_counter = Counter(node_types)
                        
                        # 检查antecedent是否在这个子图中
                        if all(type_counter.get(node_type, 0) > 0 for node_type in antecedent):
                            antecedent_support += 1
                    
                    if antecedent_support > 0:
                        confidence = support / antecedent_support
                        
                        if confidence >= min_confidence:
                            association_rules.append({
                                'antecedent': antecedent,
                                'consequent': consequent,
                                'support': support,
                                'confidence': confidence,
                                'lift': confidence / (support / len(self.subgraph_database))
                            })
        
        print(f"生成了 {len(association_rules)} 条关联规则")
        
        # 显示关联规则
        print("\n关联规则:")
        for rule in sorted(association_rules, key=lambda x: x['confidence'], reverse=True):
            antecedent_str = " ∧ ".join(rule['antecedent'])
            consequent_str = " ∧ ".join(rule['consequent'])
            print(f"  {antecedent_str} => {consequent_str}")
            print(f"    支持度: {rule['support']}, 置信度: {rule['confidence']:.3f}, 提升度: {rule['lift']:.3f}")
        
        return association_rules
        
    def visualize_frequent_patterns(self, patterns, pattern_type="node", output_file=None):
        """可视化频繁模式"""
        print(f"可视化频繁{pattern_type}模式...")
        
        if not patterns:
            print("没有模式可可视化")
            return
        
        # 创建条形图
        plt.figure(figsize=(12, 6))
        
        # 准备数据
        pattern_names = []
        supports = []
        
        for pattern, support in sorted(patterns.items(), key=lambda x: x[1], reverse=True)[:10]:
            if pattern_type == "edge":
                # 边模式：(source_type, relation_type, target_type)
                if len(pattern) == 3:
                    pattern_name = f"{pattern[0]}-{pattern[1]}-{pattern[2]}"
                else:
                    pattern_name = str(pattern)
            else:
                # 节点模式或三角形模式
                pattern_name = " + ".join(pattern)
            
            pattern_names.append(pattern_name)
            supports.append(support)
        
        # 绘制条形图
        bars = plt.bar(range(len(pattern_names)), supports, color='skyblue', alpha=0.7)
        
        # 设置标签
        plt.xlabel('Frequent Patterns')
        plt.ylabel('Support Count')
        plt.title(f'Top Frequent {pattern_type.title()} Patterns')
        plt.xticks(range(len(pattern_names)), pattern_names, rotation=45, ha='right')
        
        # 在条形上添加数值
        for i, bar in enumerate(bars):
            height = bar.get_height()
            plt.text(bar.get_x() + bar.get_width()/2., height + 0.1,
                    f'{int(height)}', ha='center', va='bottom')
        
        plt.tight_layout()
        
        # 保存图像
        if not output_file:
            output_file = str(PROJECT_ROOT / "data" / "graphs" / f"frequent_{pattern_type}_patterns.png")
        
        os.makedirs(os.path.dirname(output_file), exist_ok=True)
        plt.savefig(output_file, dpi=300, bbox_inches='tight')
        print(f"频繁{pattern_type}模式可视化保存到: {output_file}")
        
        plt.show()
        
    def save_mining_results(self, results, output_file=None):
        """保存挖掘结果"""
        if not output_file:
            output_file = str(PROJECT_ROOT / "data" / "processed" / "frequent_subgraph_mining_results.json")
        
        # 转换结果为可序列化格式
        serializable_results = {}
        for key, value in results.items():
            if isinstance(value, dict):
                # 将tuple键转换为字符串
                serializable_value = {}
                for k, v in value.items():
                    if isinstance(k, tuple):
                        serializable_value[str(k)] = v
                    else:
                        serializable_value[k] = v
                serializable_results[key] = serializable_value
            else:
                serializable_results[key] = value
        
        save_data = {
            "mining_method": "Simplified FSM",
            "subgraph_database_size": len(self.subgraph_database),
            "main_graph_stats": {
                "num_nodes": len(self.main_graph.nodes()),
                "num_edges": len(self.main_graph.edges())
            },
            "results": serializable_results,
            "generated_at": __import__('datetime').datetime.now().isoformat()
        }
        
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(save_data, f, ensure_ascii=False, indent=2)
        
        print(f"挖掘结果保存到: {output_file}")
        return output_file


def run_frequent_subgraph_mining():
    """运行频繁子图挖掘"""
    print("=== 频繁子图挖掘 (关联规则) ===")
    
    miner = FrequentSubgraphMiner()
    
    # 加载数据
    if not miner.load_data():
        return None
    
    # 构建主图
    if not miner.build_main_graph():
        print("无法构建主图")
        return None
    
    # 提取子图数据库
    if not miner.extract_ego_networks(radius=1):
        print("无法提取子图数据库")
        return None
    
    # 挖掘不同类型的频繁模式
    results = {}
    
    print("\n--- 挖掘频繁节点类型模式 ---")
    frequent_nodes = miner.mine_frequent_node_patterns(min_support=2)
    results['frequent_node_patterns'] = frequent_nodes
    
    print("\n--- 挖掘频繁边模式 ---")
    frequent_edges = miner.mine_frequent_edge_patterns(min_support=2)
    results['frequent_edge_patterns'] = frequent_edges
    
    print("\n--- 挖掘频繁三角形模式 ---")
    frequent_triangles = miner.mine_frequent_triangles(min_support=1)
    results['frequent_triangle_patterns'] = frequent_triangles
    
    print("\n--- 生成关联规则 ---")
    association_rules = miner.generate_association_rules(frequent_nodes, min_confidence=0.5)
    results['association_rules'] = association_rules
    
    # 可视化结果
    try:
        miner.visualize_frequent_patterns(frequent_nodes, "node")
        miner.visualize_frequent_patterns(frequent_edges, "edge") 
    except Exception as e:
        print(f"可视化失败: {e}")
    
    # 保存结果
    output_file = miner.save_mining_results(results)
    
    return miner


if __name__ == "__main__":
    # 运行频繁子图挖掘
    miner = run_frequent_subgraph_mining()
    
    print("\n=== 频繁子图挖掘完成 ===")
    print("结果文件: frequent_subgraph_mining_results.json")
    print("可视化文件: frequent_node_patterns.png, frequent_edge_patterns.png")
    print("\n这个模块将传统的关联规则挖掘扩展到了图数据，")
    print("发现了论文、作者、主题之间的频繁共现模式和关联规则！")
