"""
Complete Knowledge Graph Mining Workflow (Fixed Version)
完整的知识图谱挖掘工作流程（修复版本）
"""

import os
import sys
import time
from pathlib import Path

# Project setup
PROJECT_ROOT = Path(__file__).parent.absolute()
sys.path.insert(0, str(PROJECT_ROOT))

def print_header():
    """打印工作流程标题"""
    print("🎯 COMPLETE KNOWLEDGE GRAPH MINING WORKFLOW (FIXED VERSION)")
    print("=" * 80)
    print("This workflow uses the FIXED modules that eliminate data leakage")
    print("and mathematical errors, providing scientifically valid results.")
    print("=" * 80)

def check_data_availability():
    """检查数据文件是否存在"""
    print("\n📊 STEP 1: CHECKING DATA AVAILABILITY")
    print("-" * 50)
    
    data_file = PROJECT_ROOT / "data" / "processed" / "extracted_entities_relations.json"
    
    if data_file.exists():
        file_size = data_file.stat().st_size / 1024  # KB
        print(f"✅ Data file found: {data_file}")
        print(f"   File size: {file_size:.1f} KB")
        return True
    else:
        print(f"❌ Data file not found: {data_file}")
        print("\n🔧 To generate data, run:")
        print("   1. python src/knowledge_construction/extractor.py")
        print("   2. Or use the AI intelligent workflow")
        return False

def run_knowledge_extraction():
    """运行知识提取（如果需要）"""
    print("\n🔍 STEP 2: KNOWLEDGE EXTRACTION")
    print("-" * 50)
    
    data_file = PROJECT_ROOT / "data" / "processed" / "extracted_entities_relations.json"
    
    if not data_file.exists():
        print("Running knowledge extraction...")
        try:
            from src.knowledge_construction.extractor import main as extract_main
            extract_main()
            print("✅ Knowledge extraction completed")
            return True
        except Exception as e:
            print(f"❌ Knowledge extraction failed: {e}")
            return False
    else:
        print("✅ Data already available, skipping extraction")
        return True

def run_fixed_data_mining():
    """运行修复版本的数据挖掘"""
    print("\n🎯 STEP 3: FIXED DATA MINING ANALYSIS")
    print("-" * 50)
    print("Using scientifically corrected modules:")
    print("  • Classification: No data leakage")
    print("  • Association Rules: Valid confidence ≤ 1.0")
    print("  • Clustering: Enhanced analysis")
    
    try:
        # Import and run fixed modules
        print("\n🔧 Running fixed classification...")
        from src.data_mining.classification_enhanced_fixed import run_fixed_classification
        
        start_time = time.time()
        classifier = run_fixed_classification()
        class_time = time.time() - start_time
        
        if classifier:
            print(f"✅ Fixed classification completed in {class_time:.2f} seconds")
        else:
            print("❌ Fixed classification failed")
            return False
        
        print("\n🔧 Running fixed association rules...")
        from src.data_mining.association_rules_enhanced_fixed import run_fixed_gspan_mining
        
        start_time = time.time()
        miner = run_fixed_gspan_mining()
        assoc_time = time.time() - start_time
        
        if miner:
            print(f"✅ Fixed association rules completed in {assoc_time:.2f} seconds")
        else:
            print("❌ Fixed association rules failed")
            return False
        
        print("\n🔧 Running clustering analysis...")
        from src.data_mining.clustering import run_author_clustering, run_paper_topic_clustering
        
        start_time = time.time()
        
        # Author clustering
        author_analyzer = run_author_clustering()
        if author_analyzer:
            print("✅ Author clustering completed")
        
        # Paper-topic clustering
        topic_analyzer = run_paper_topic_clustering()
        if topic_analyzer:
            print("✅ Paper-topic clustering completed")
        
        cluster_time = time.time() - start_time
        print(f"✅ Clustering analysis completed in {cluster_time:.2f} seconds")
        
        return True
        
    except Exception as e:
        print(f"❌ Data mining failed: {e}")
        return False

def validate_results():
    """验证结果质量"""
    print("\n🔍 STEP 4: RESULT VALIDATION")
    print("-" * 50)
    
    try:
        # Run validation
        print("Running comprehensive validation...")
        
        # Import validation functions
        import json
        
        # Check classification results
        class_file = PROJECT_ROOT / "data" / "processed" / "fixed_classification_results.json"
        if class_file.exists():
            with open(class_file, 'r', encoding='utf-8') as f:
                class_results = json.load(f)
            
            test_acc = class_results['random_forest_results']['test_accuracy']
            cv_mean = class_results['random_forest_results']['cv_mean']
            cv_std = class_results['random_forest_results']['cv_std']
            
            print(f"📊 Classification Results:")
            print(f"   Test Accuracy: {test_acc:.3f} ({test_acc*100:.1f}%)")
            print(f"   CV Accuracy: {cv_mean:.3f} ± {cv_std:.3f}")
            
            if test_acc < 0.999:
                print("   ✅ Realistic accuracy (no data leakage)")
            else:
                print("   ⚠️  Suspiciously high accuracy")
        
        # Check association rules results
        assoc_file = PROJECT_ROOT / "data" / "processed" / "fixed_association_rules_results.json"
        if assoc_file.exists():
            with open(assoc_file, 'r', encoding='utf-8') as f:
                assoc_results = json.load(f)
            
            rules = assoc_results['association_rules']
            invalid_rules = [r for r in rules if r['confidence'] > 1.0]
            max_confidence = max([r['confidence'] for r in rules]) if rules else 0
            
            print(f"\n📊 Association Rules Results:")
            print(f"   Total Rules: {len(rules)}")
            print(f"   Invalid Rules (>1.0): {len(invalid_rules)}")
            print(f"   Max Confidence: {max_confidence:.3f} ({max_confidence*100:.1f}%)")
            
            if len(invalid_rules) == 0:
                print("   ✅ All confidence values mathematically valid")
            else:
                print("   ❌ Some confidence values still invalid")
        
        return True
        
    except Exception as e:
        print(f"❌ Validation failed: {e}")
        return False

def check_output_files():
    """检查输出文件"""
    print("\n📁 STEP 5: OUTPUT FILES CHECK")
    print("-" * 50)
    
    expected_files = [
        "data/processed/fixed_classification_results.json",
        "data/processed/fixed_association_rules_results.json",
        "data/graphs/enhanced_classification_results.png",
        "data/graphs/frequent_subgraphs_gspan.png",
        "data/graphs/author_collaboration_clusters.png",
        "data/graphs/paper_topic_clusters.png"
    ]
    
    found_files = 0
    total_files = len(expected_files)
    
    for file_path in expected_files:
        full_path = PROJECT_ROOT / file_path
        if full_path.exists():
            file_size = full_path.stat().st_size / 1024  # KB
            print(f"✅ {file_path} ({file_size:.1f} KB)")
            found_files += 1
        else:
            print(f"❌ {file_path} (missing)")
    
    print(f"\n📊 File Summary: {found_files}/{total_files} files found ({found_files/total_files*100:.1f}%)")
    
    return found_files >= total_files * 0.8  # 80% success rate

def print_summary(success_steps):
    """打印工作流程总结"""
    print("\n" + "=" * 80)
    print("🎯 WORKFLOW COMPLETION SUMMARY")
    print("=" * 80)
    
    total_steps = 5
    success_rate = len(success_steps) / total_steps * 100
    
    print(f"Completed Steps: {len(success_steps)}/{total_steps} ({success_rate:.1f}%)")
    
    step_names = [
        "Data Availability Check",
        "Knowledge Extraction", 
        "Fixed Data Mining",
        "Result Validation",
        "Output Files Check"
    ]
    
    for i, step_name in enumerate(step_names, 1):
        status = "✅" if i in success_steps else "❌"
        print(f"  {status} Step {i}: {step_name}")
    
    if len(success_steps) == total_steps:
        print(f"\n🎉 WORKFLOW COMPLETED SUCCESSFULLY!")
        print(f"✅ All data mining modules executed with scientific validity")
        print(f"✅ Results are ready for analysis and publication")
        
        print(f"\n📊 Key Results:")
        print(f"   • Classification: Realistic accuracy (no data leakage)")
        print(f"   • Association Rules: Valid confidence values (≤ 100%)")
        print(f"   • Clustering: Enhanced community detection")
        print(f"   • Visualizations: Comprehensive graph analysis")
        
        print(f"\n📁 Results Location:")
        print(f"   • JSON Results: data/processed/")
        print(f"   • Visualizations: data/graphs/")
        
        print(f"\n🔍 Next Steps:")
        print(f"   • Review results in data/processed/")
        print(f"   • Analyze visualizations in data/graphs/")
        print(f"   • Run performance_comparison.py for detailed analysis")
        print(f"   • Use results for academic publication")
        
    else:
        print(f"\n⚠️  WORKFLOW PARTIALLY COMPLETED")
        print(f"Some steps failed - please check the error messages above")
        print(f"You can run individual modules manually if needed")

def main():
    """主工作流程"""
    print_header()
    
    success_steps = []
    
    # Step 1: Check data availability
    if check_data_availability():
        success_steps.append(1)
    
    # Step 2: Knowledge extraction (if needed)
    if run_knowledge_extraction():
        success_steps.append(2)
    
    # Step 3: Fixed data mining
    if run_fixed_data_mining():
        success_steps.append(3)
    
    # Step 4: Validate results
    if validate_results():
        success_steps.append(4)
    
    # Step 5: Check output files
    if check_output_files():
        success_steps.append(5)
    
    # Summary
    print_summary(success_steps)
    
    return len(success_steps) == 5

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
