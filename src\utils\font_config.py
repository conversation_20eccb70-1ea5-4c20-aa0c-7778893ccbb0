"""
字体配置模块 - 解决matplotlib中文显示问题
为项目中的图像生成统一配置中文字体
"""

import matplotlib.pyplot as plt
import matplotlib as mpl
import platform
import os
from pathlib import Path

def setup_chinese_font():
    """
    配置matplotlib中文字体
    自动检测系统并设置合适的中文字体
    """
    try:
        # 获取系统类型
        system = platform.system()
        
        # 根据系统选择字体
        if system == "Windows":
            # Windows系统字体
            fonts = [
                'Microsoft YaHei',    # 微软雅黑
                'SimHei',            # 黑体
                'SimSun',            # 宋体
                'KaiTi',             # 楷体
                'FangSong'           # 仿宋
            ]
        elif system == "Darwin":  # macOS
            # macOS系统字体
            fonts = [
                'PingFang SC',       # 苹方-简
                'Hiragino Sans GB',  # 冬青黑体简体中文
                'STSong',            # 华文宋体
                'STHeiti',           # 华文黑体
                'Arial Unicode MS'   # Arial Unicode MS
            ]
        else:  # Linux
            # Linux系统字体
            fonts = [
                'WenQuanYi Micro Hei',    # 文泉驿微米黑
                'WenQuanYi Zen Hei',      # 文泉驿正黑
                'Noto Sans CJK SC',       # 思源黑体
                'Source Han Sans CN',     # 思源黑体
                'DejaVu Sans'             # DejaVu Sans
            ]
        
        # 尝试设置字体
        font_set = False
        for font in fonts:
            try:
                # 测试字体是否可用
                test_font = mpl.font_manager.FontProperties(family=font)
                if test_font.get_name() in [f.name for f in mpl.font_manager.fontManager.ttflist]:
                    plt.rcParams['font.sans-serif'] = [font] + plt.rcParams['font.sans-serif']
                    plt.rcParams['axes.unicode_minus'] = False  # 解决负号显示问题
                    print(f"成功设置中文字体: {font}")
                    font_set = True
                    break
            except Exception as e:
                continue
        
        if not font_set:
            print("警告: 未找到合适的中文字体，将使用系统默认字体")
            print("可能会出现中文显示问题")
            # 设置基本的unicode处理
            plt.rcParams['axes.unicode_minus'] = False
            
    except Exception as e:
        print(f"字体配置失败: {e}")
        print("将使用默认字体设置")
        plt.rcParams['axes.unicode_minus'] = False

def get_available_chinese_fonts():
    """
    获取系统中可用的中文字体列表
    """
    try:
        available_fonts = []
        
        # 获取所有字体
        font_list = mpl.font_manager.fontManager.ttflist
        
        # 中文字体关键词
        chinese_keywords = [
            'SimHei', 'SimSun', 'Microsoft YaHei', 'KaiTi', 'FangSong',  # Windows
            'PingFang', 'Hiragino', 'STSong', 'STHeiti',                # macOS
            'WenQuanYi', 'Noto Sans CJK', 'Source Han Sans'             # Linux
        ]
        
        for font in font_list:
            font_name = font.name
            # 检查是否包含中文字体关键词
            if any(keyword in font_name for keyword in chinese_keywords):
                if font_name not in available_fonts:
                    available_fonts.append(font_name)
        
        return available_fonts
        
    except Exception as e:
        print(f"获取字体列表失败: {e}")
        return []

def test_chinese_display():
    """
    测试中文字体显示效果
    """
    try:
        import matplotlib.pyplot as plt
        
        # 创建测试图
        fig, ax = plt.subplots(figsize=(8, 6))
        
        # 测试文本
        test_texts = [
            "知识图谱挖掘项目",
            "聚类分析结果",
            "分类预测效果", 
            "关联规则发现",
            "数据挖掘可视化"
        ]
        
        y_positions = [0.8, 0.6, 0.4, 0.2, 0.0]
        
        for i, text in enumerate(test_texts):
            ax.text(0.5, y_positions[i], text, 
                   fontsize=14, ha='center', va='center',
                   transform=ax.transAxes)
        
        ax.set_xlim(0, 1)
        ax.set_ylim(-0.1, 1)
        ax.set_title("中文字体显示测试", fontsize=16)
        ax.axis('off')
        
        plt.tight_layout()
        
        # 保存测试图像
        output_path = Path(__file__).parent.parent.parent / "data" / "graphs" / "font_test.png"
        output_path.parent.mkdir(parents=True, exist_ok=True)
        plt.savefig(output_path, dpi=300, bbox_inches='tight')
        
        print(f"字体测试图像已保存: {output_path}")
        plt.close()
        
        return str(output_path)
        
    except Exception as e:
        print(f"字体测试失败: {e}")
        return None

# 初始化字体配置
def init_font_config():
    """
    初始化字体配置
    在导入此模块时自动调用
    """
    print("正在配置matplotlib中文字体...")
    setup_chinese_font()
    
    # 显示可用的中文字体
    available_fonts = get_available_chinese_fonts()
    if available_fonts:
        print(f"系统中可用的中文字体: {', '.join(available_fonts[:5])}")
        if len(available_fonts) > 5:
            print(f"  ... 还有 {len(available_fonts) - 5} 个字体")
    
    print("字体配置完成")

# 模块导入时自动初始化
if __name__ != "__main__":
    init_font_config()

# 如果直接运行此文件，进行字体测试
if __name__ == "__main__":
    print("=== 字体配置测试 ===")
    init_font_config()
    
    print("\n=== 可用字体列表 ===")
    fonts = get_available_chinese_fonts()
    for i, font in enumerate(fonts, 1):
        print(f"{i}. {font}")
    
    print("\n=== 字体显示测试 ===")
    test_path = test_chinese_display()
    if test_path:
        print(f"请检查测试图像: {test_path}")