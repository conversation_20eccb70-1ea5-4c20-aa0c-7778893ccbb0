"""
Comprehensive Test Suite for Fixed Data Mining Modules
确保修复后的模块正确性和防止回归的测试套件
"""

import unittest
import json
import numpy as np
import networkx as nx
from pathlib import Path
import sys
import tempfile
import os

# Project setup
PROJECT_ROOT = Path(__file__).parent.parent.absolute()
sys.path.insert(0, str(PROJECT_ROOT))

class TestDataLeakageFix(unittest.TestCase):
    """测试数据泄露修复"""
    
    def setUp(self):
        """设置测试环境"""
        from src.data_mining.classification_enhanced_fixed import FixedEnhancedGraphClassifier
        self.classifier = FixedEnhancedGraphClassifier()
        
        # Create test data
        self.test_data = {
            'entities': {
                'Paper': ['P1', 'P2', 'P3'],
                'Author': ['A1', 'A2', 'A3', 'A4'],
                'Topic': ['T1', 'T2'],
                'Subject': ['S1', 'S2']
            },
            'relations': [
                {'source': 'A1', 'target': 'P1', 'type': 'AUTHORED_BY'},
                {'source': 'A2', 'target': 'P2', 'type': 'AUTHORED_BY'},
                {'source': 'P1', 'target': 'T1', 'type': 'HAS_TOPIC'},
                {'source': 'P2', 'target': 'T2', 'type': 'HAS_TOPIC'},
                {'source': 'P1', 'target': 'S1', 'type': 'BELONGS_TO'},
                {'source': 'P2', 'target': 'S2', 'type': 'BELONGS_TO'}
            ]
        }
        
        # Save test data to temporary file
        self.temp_file = tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False)
        json.dump(self.test_data, self.temp_file, ensure_ascii=False, indent=2)
        self.temp_file.close()
        
        self.classifier.data_file = self.temp_file.name
    
    def tearDown(self):
        """清理测试环境"""
        if os.path.exists(self.temp_file.name):
            os.unlink(self.temp_file.name)
    
    def test_proper_train_test_split(self):
        """测试正确的训练/测试分离"""
        # Load and build graph
        self.assertTrue(self.classifier.load_data())
        self.assertTrue(self.classifier.build_heterogeneous_graph())
        
        # Create train/test split
        self.assertTrue(self.classifier.create_train_test_split(test_size=0.3))
        
        # Verify split
        self.assertGreater(len(self.classifier.train_nodes), 0)
        self.assertGreater(len(self.classifier.test_nodes), 0)
        
        # Verify no overlap
        overlap = self.classifier.train_nodes.intersection(self.classifier.test_nodes)
        self.assertEqual(len(overlap), 0, "Train and test sets should not overlap")
        
        # Verify all nodes are covered
        all_nodes = set(self.classifier.graph.nodes())
        split_nodes = self.classifier.train_nodes.union(self.classifier.test_nodes)
        self.assertEqual(all_nodes, split_nodes, "All nodes should be in either train or test set")
    
    def test_feature_isolation(self):
        """测试特征隔离"""
        # Setup
        self.classifier.load_data()
        self.classifier.build_heterogeneous_graph()
        self.classifier.create_train_test_split(test_size=0.3)
        
        # Create features
        self.assertTrue(self.classifier.create_safe_features())
        
        # Verify train features exist
        self.assertGreater(len(self.classifier.train_features), 0)
        self.assertGreater(len(self.classifier.test_features), 0)
        
        # Verify feature dimensions are consistent
        train_feature_dims = [len(features) for features in self.classifier.train_features.values()]
        test_feature_dims = [len(features) for features in self.classifier.test_features.values()]
        
        self.assertTrue(all(dim == train_feature_dims[0] for dim in train_feature_dims))
        self.assertTrue(all(dim == test_feature_dims[0] for dim in test_feature_dims))
        self.assertEqual(train_feature_dims[0], test_feature_dims[0])
    
    def test_no_perfect_accuracy(self):
        """测试不会出现完美准确率（除非数据真的很简单）"""
        # This test ensures that with proper data isolation,
        # we don't get suspiciously perfect results
        
        # Setup and run classification
        self.classifier.load_data()
        self.classifier.build_heterogeneous_graph()
        self.classifier.create_train_test_split(test_size=0.3)
        self.classifier.create_safe_features()
        
        X_train, X_test, y_train, y_test = self.classifier.prepare_ml_data()
        results = self.classifier.train_fixed_random_forest(X_train, X_test, y_train, y_test)
        
        # With proper isolation, perfect accuracy should be rare
        # (unless the dataset is trivially separable)
        test_accuracy = results['test_accuracy']
        
        # Allow perfect accuracy only if test set is very small
        if len(y_test) > 3:
            self.assertLess(test_accuracy, 0.999, 
                          f"Test accuracy {test_accuracy:.3f} is suspiciously high for test set size {len(y_test)}")


class TestConfidenceValidation(unittest.TestCase):
    """测试置信度验证修复"""
    
    def setUp(self):
        """设置测试环境"""
        from src.data_mining.association_rules_enhanced_fixed import FixedEnhancedAssociationRulesMiner
        self.miner = FixedEnhancedAssociationRulesMiner()
        
        # Create test data with known patterns
        self.test_data = {
            'entities': {
                'Paper': ['P1', 'P2', 'P3', 'P4'],
                'Author': ['A1', 'A2', 'A3'],
                'Topic': ['T1', 'T2'],
                'Subject': ['S1', 'S2']
            },
            'relations': [
                {'source': 'A1', 'target': 'P1', 'type': 'AUTHORED_BY'},
                {'source': 'A1', 'target': 'P2', 'type': 'AUTHORED_BY'},
                {'source': 'A2', 'target': 'P3', 'type': 'AUTHORED_BY'},
                {'source': 'P1', 'target': 'T1', 'type': 'HAS_TOPIC'},
                {'source': 'P2', 'target': 'T1', 'type': 'HAS_TOPIC'},
                {'source': 'P3', 'target': 'T2', 'type': 'HAS_TOPIC'},
                {'source': 'P1', 'target': 'S1', 'type': 'BELONGS_TO'},
                {'source': 'P2', 'target': 'S1', 'type': 'BELONGS_TO'},
                {'source': 'P3', 'target': 'S2', 'type': 'BELONGS_TO'}
            ]
        }
        
        # Save test data
        self.temp_file = tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False)
        json.dump(self.test_data, self.temp_file, ensure_ascii=False, indent=2)
        self.temp_file.close()
        
        self.miner.data_file = self.temp_file.name
    
    def tearDown(self):
        """清理测试环境"""
        if os.path.exists(self.temp_file.name):
            os.unlink(self.temp_file.name)
    
    def test_confidence_bounds(self):
        """测试置信度边界"""
        # Setup
        self.miner.load_data()
        self.miner.build_main_graph()
        self.miner.create_graph_database()
        self.miner.run_gspan_mining(min_support=1)
        
        # Generate rules
        rules = self.miner.generate_fixed_association_rules(min_confidence=0.1, min_lift=1.0)
        
        # Verify all confidence values are valid
        for rule in rules:
            confidence = rule['confidence']
            self.assertGreaterEqual(confidence, 0.0, 
                                  f"Confidence {confidence} should be >= 0")
            self.assertLessEqual(confidence, 1.0, 
                               f"Confidence {confidence} should be <= 1.0")
            
            # Verify lift is positive
            lift = rule['lift']
            self.assertGreater(lift, 0.0, f"Lift {lift} should be > 0")
    
    def test_support_consistency(self):
        """测试支持度一致性"""
        # Setup
        self.miner.load_data()
        self.miner.build_main_graph()
        self.miner.create_graph_database()
        
        # Create a simple test case
        antecedent = ('Paper',)
        consequent = ('Topic',)
        support = 3
        
        # Test the fixed calculation
        confidence, lift = self.miner._calculate_fixed_rule_metrics(antecedent, consequent, support)
        
        # Confidence should be valid
        self.assertGreaterEqual(confidence, 0.0)
        self.assertLessEqual(confidence, 1.0)
    
    def test_mathematical_assertions(self):
        """测试数学断言"""
        # This test ensures that the mathematical assertions in the code work correctly
        
        # Setup
        self.miner.load_data()
        self.miner.build_main_graph()
        self.miner.create_graph_database()
        self.miner.run_gspan_mining(min_support=1)
        
        # Generate rules - this should not raise any assertion errors
        try:
            rules = self.miner.generate_fixed_association_rules(min_confidence=0.1, min_lift=1.0)
            # If we get here, all assertions passed
            self.assertTrue(True)
        except AssertionError as e:
            self.fail(f"Mathematical assertion failed: {e}")


class TestResultsValidation(unittest.TestCase):
    """测试结果验证"""
    
    def test_classification_results_format(self):
        """测试分类结果格式"""
        results_file = PROJECT_ROOT / "data" / "processed" / "fixed_classification_results.json"
        
        if results_file.exists():
            with open(results_file, 'r', encoding='utf-8') as f:
                results = json.load(f)
            
            # Check required fields
            self.assertIn('random_forest_results', results)
            self.assertIn('data_leakage_fixed', results)
            self.assertTrue(results['data_leakage_fixed'])
            
            # Check accuracy bounds
            rf_results = results['random_forest_results']
            test_accuracy = rf_results['test_accuracy']
            self.assertGreaterEqual(test_accuracy, 0.0)
            self.assertLessEqual(test_accuracy, 1.0)
            
            # Check CV results
            cv_mean = rf_results['cv_mean']
            cv_std = rf_results['cv_std']
            self.assertGreaterEqual(cv_mean, 0.0)
            self.assertLessEqual(cv_mean, 1.0)
            self.assertGreaterEqual(cv_std, 0.0)
    
    def test_association_results_format(self):
        """测试关联规则结果格式"""
        results_file = PROJECT_ROOT / "data" / "processed" / "fixed_association_rules_results.json"
        
        if results_file.exists():
            with open(results_file, 'r', encoding='utf-8') as f:
                results = json.load(f)
            
            # Check required fields
            self.assertIn('association_rules', results)
            self.assertIn('confidence_validation', results)
            
            # Check validation summary
            validation = results['validation_summary']
            self.assertTrue(validation['all_confidence_valid'])
            self.assertLessEqual(validation['max_confidence'], 1.0)
            self.assertGreaterEqual(validation['min_confidence'], 0.0)
            
            # Check individual rules
            rules = results['association_rules']
            for rule in rules:
                confidence = rule['confidence']
                self.assertGreaterEqual(confidence, 0.0)
                self.assertLessEqual(confidence, 1.0)
                
                lift = rule['lift']
                self.assertGreater(lift, 0.0)


class TestRegressionPrevention(unittest.TestCase):
    """测试回归预防"""
    
    def test_no_data_leakage_regression(self):
        """测试不会回归到数据泄露"""
        # This test ensures that the fixed classification module
        # maintains proper data isolation
        
        from src.data_mining.classification_enhanced_fixed import FixedEnhancedGraphClassifier
        
        # Check that the class has the required isolation methods
        classifier = FixedEnhancedGraphClassifier()
        
        # Verify isolation attributes exist
        self.assertTrue(hasattr(classifier, 'train_nodes'))
        self.assertTrue(hasattr(classifier, 'test_nodes'))
        self.assertTrue(hasattr(classifier, 'train_features'))
        self.assertTrue(hasattr(classifier, 'test_features'))
        
        # Verify isolation methods exist
        self.assertTrue(hasattr(classifier, 'create_train_test_split'))
        self.assertTrue(hasattr(classifier, 'create_safe_features'))
    
    def test_no_confidence_regression(self):
        """测试不会回归到无效置信度"""
        # This test ensures that the fixed association rules module
        # maintains confidence validation
        
        from src.data_mining.association_rules_enhanced_fixed import FixedEnhancedAssociationRulesMiner
        
        # Check that the class has the required validation methods
        miner = FixedEnhancedAssociationRulesMiner()
        
        # Verify validation methods exist
        self.assertTrue(hasattr(miner, '_calculate_fixed_rule_metrics'))
        self.assertTrue(hasattr(miner, '_calculate_fixed_edge_node_rule_metrics'))
        self.assertTrue(hasattr(miner, 'generate_fixed_association_rules'))


def run_tests():
    """运行所有测试"""
    print("🧪 RUNNING COMPREHENSIVE TEST SUITE")
    print("=" * 60)
    
    # Create test suite
    test_suite = unittest.TestSuite()
    
    # Add test classes
    test_classes = [
        TestDataLeakageFix,
        TestConfidenceValidation,
        TestResultsValidation,
        TestRegressionPrevention
    ]
    
    for test_class in test_classes:
        tests = unittest.TestLoader().loadTestsFromTestCase(test_class)
        test_suite.addTests(tests)
    
    # Run tests
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)
    
    # Summary
    print("\n" + "=" * 60)
    print("🎯 TEST SUMMARY")
    print("=" * 60)
    
    total_tests = result.testsRun
    failures = len(result.failures)
    errors = len(result.errors)
    successes = total_tests - failures - errors
    
    print(f"Total Tests: {total_tests}")
    print(f"Successes: {successes}")
    print(f"Failures: {failures}")
    print(f"Errors: {errors}")
    
    if failures == 0 and errors == 0:
        print("\n✅ ALL TESTS PASSED!")
        print("🎉 Fixed modules are working correctly")
        print("🛡️  No regressions detected")
    else:
        print(f"\n❌ {failures + errors} TESTS FAILED")
        print("⚠️  Please review and fix the issues")
    
    return result.wasSuccessful()


if __name__ == "__main__":
    success = run_tests()
    sys.exit(0 if success else 1)
