"""
数据抓取主程序
使用crawl4ai MCP服务抓取ArXiv论文数据
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.data_acquisition.crawler import ArxivCrawler
import json


def main():
    """主函数：执行数据抓取流程"""
    print("=== ArXiv论文数据抓取程序 ===")
    
    # 初始化爬虫
    crawler = ArxivCrawler()
    
    # 获取要爬取的ArXiv ID列表
    arxiv_ids = crawler.generate_arxiv_ids()
    print(f"准备抓取 {len(arxiv_ids)} 篇论文:")
    for i, arxiv_id in enumerate(arxiv_ids, 1):
        print(f"  {i}. https://arxiv.org/abs/{arxiv_id}")
    
    print("\n请按照以下步骤使用crawl4ai MCP工具:")
    print("="*50)
    
    for i, arxiv_id in enumerate(arxiv_ids, 1):
        url = f"https://arxiv.org/abs/{arxiv_id}"
        print(f"\n步骤 {i}: 抓取论文 {arxiv_id}")
        print(f"URL: {url}")
        print("MCP调用:")
        print(f'crawl4ai:md')
        print(f'参数: url="{url}", f="fit"')
        print("-" * 30)
    
    print("\n抓取完成后，请将结果传递给process_crawl_result()函数")
    print("示例调用:")
    print("process_crawl_result(arxiv_id, markdown_content)")


def process_crawl_result(arxiv_id: str, markdown_content: str):
    """处理crawl4ai抓取的结果"""
    crawler = ArxivCrawler()
    
    print(f"\n正在处理论文: {arxiv_id}")
    
    # 提取论文信息
    paper_info = crawler.extract_paper_info(markdown_content, arxiv_id)
    
    # 保存数据
    filepath = crawler.save_paper_data(paper_info)
    
    print(f"论文信息提取完成:")
    print(f"  标题: {paper_info['title']}")
    print(f"  作者: {len(paper_info['authors'])} 人")
    print(f"  摘要长度: {len(paper_info['abstract'])} 字符")
    print(f"  学科: {len(paper_info['subjects'])} 个")
    print(f"  保存到: {filepath}")
    
    return paper_info


def show_crawled_data():
    """显示已抓取的数据统计"""
    data_dir = "data/raw"
    if not os.path.exists(data_dir):
        print("数据目录不存在")
        return
    
    json_files = [f for f in os.listdir(data_dir) if f.endswith('.json')]
    
    print(f"\n=== 已抓取数据统计 ===")
    print(f"总文件数: {len(json_files)}")
    
    papers = []
    for filename in json_files:
        filepath = os.path.join(data_dir, filename)
        with open(filepath, 'r', encoding='utf-8') as f:
            paper = json.load(f)
            papers.append(paper)
    
    if papers:
        print(f"论文数量: {len(papers)}")
        print(f"作者总数: {sum(len(p['authors']) for p in papers)}")
        print(f"学科类别: {sum(len(p['subjects']) for p in papers)}")
        
        print("\n论文列表:")
        for i, paper in enumerate(papers, 1):
            print(f"  {i}. {paper['title'][:50]}...")
    
    return papers


if __name__ == "__main__":
    main()
    
    # 显示已有数据
    show_crawled_data()
