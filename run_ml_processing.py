#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import json
import os
from process_ai_crawl_result import process_ai_crawl_result

def run_ml_paper_processing():
    """运行机器学习论文处理"""
    
    # 读取我们之前保存的机器学习论文数据
    crawl_data_path = r"C:\dev\MCP\knowledge-graph-mining\data\raw\machine_learning_papers_crawl.json"
    
    if not os.path.exists(crawl_data_path):
        print(f"错误: 找不到文件 {crawl_data_path}")
        return
    
    with open(crawl_data_path, 'r', encoding='utf-8') as f:
        crawl_data = json.load(f)
    
    # 将JSON数据转换为模拟crawl4ai的markdown格式
    markdown_content = convert_json_to_markdown(crawl_data)
    
    # 运行AI智能处理
    research_query = "Recent machine learning advances and applications"
    source_url = crawl_data.get("url", "https://arxiv.org/list/cs.LG/recent")
    
    print(f"开始处理机器学习论文数据...")
    print(f"论文数量: {len(crawl_data.get('selected_papers', []))}")
    
    result = process_ai_crawl_result(markdown_content, source_url, research_query)
    
    return result

def convert_json_to_markdown(crawl_data):
    """将JSON数据转换为markdown格式"""
    
    papers = crawl_data.get("selected_papers", [])
    
    markdown_lines = [
        "# Machine Learning",
        "## Authors and titles for recent submissions",
        "",
        f"Total of {crawl_data.get('total_papers', len(papers))} entries",
        ""
    ]
    
    for i, paper in enumerate(papers, 1):
        arxiv_id = paper.get("id", "")
        title = paper.get("title", "")
        authors = paper.get("authors", [])
        comments = paper.get("comments", "")
        subjects = paper.get("subjects", [])
        
        # 构建markdown格式
        markdown_lines.append(f"[{i}] arXiv:{arxiv_id}")
        markdown_lines.append(f"Title: {title}")
        
        if authors:
            authors_str = ", ".join(authors)
            markdown_lines.append(f"Authors: <AUTHORS>
        
        if comments:
            markdown_lines.append(f"Comments: {comments}")
        
        if subjects:
            subjects_str = "; ".join(subjects)
            markdown_lines.append(f"Subjects: {subjects_str}")
        
        markdown_lines.append("")
    
    return "\n".join(markdown_lines)

if __name__ == "__main__":
    result = run_ml_paper_processing()
    print(f"\n处理完成！")
