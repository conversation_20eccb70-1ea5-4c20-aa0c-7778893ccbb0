"""
批量处理所有抓取的论文数据
"""

import sys
import os
from pathlib import Path

# 设置项目路径
PROJECT_ROOT = Path(r"C:\dev\MCP\knowledge-graph-mining")
sys.path.insert(0, str(PROJECT_ROOT))

from src.data_acquisition.crawler import ArxivCrawler

def process_all_papers():
    """处理所有抓取的论文数据"""
    
    # 创建爬虫实例
    crawler = ArxivCrawler(output_dir=str(PROJECT_ROOT / "data" / "raw"))
    
    # 论文数据列表
    papers_data = [
        {
            "arxiv_id": "2301.00001",
            "markdown": """# Computer Science > Human-Computer Interaction
**arXiv:2301.00001** (cs) 
[Submitted on 21 Dec 2022]
#  Title:NFTrig
Authors: <AUTHORS>
> Abstract:NFTrig is a web-based application created for use as an educational tool to teach trigonometry and block chain technology. Creation of the application includes front and back end development as well as integration with other outside sources including MetaMask and OpenSea. The primary development languages include HTML, CSS (Bootstrap 5), and JavaScript as well as Solidity for smart contract creation. The application itself is hosted on Moralis utilizing their Web3 API. This technical report describes how the application was created, what the application requires, and smart contract design with security considerations in mind. The NFTrig application has underwent significant testing and validation prior to and after deployment. Future suggestions and recommendations for further development, maintenance, and use in other fields for education are also described.
Subjects: |  Human-Computer Interaction (cs.HC)  
Cite as: | [arXiv:2301.00001](https://arxiv.org/abs/2301.00001) [cs.HC]  
From: Tauheed Khan Mohd [[view email](https://arxiv.org/show-email/b39d47e2/2301.00001)] **[v1]** Wed, 21 Dec 2022 18:07:06 UTC (1,260 KB)"""
        },
        {
            "arxiv_id": "2301.00123", 
            "markdown": """# High Energy Physics - Theory
**arXiv:2301.00123** (hep-th) 
[Submitted on 31 Dec 2022]
#  Title:Velocity Laws for Bound States in Asymptotically AdS Geometries
Authors: <AUTHORS>
> Abstract:We study the behavior of heavy quark bound states in moving plasmas that are dual to theories with generic non-trivial renormalization group flows interpolating between an AdS geometry in the ultraviolet and infrared fixed points with broken symmetries. We investigate analytically the observables associated with the bound state and find their scaling exponents with respect to the Lorentz factor for ultrarelativistic motion. Despite having asymptotically an AdS geometry, the scaling is not universal and depends on geometric conditions of the Fefferman-Graham expansion in the near boundary regime, or equivalently on the order of the asymptotic background expansion that provides the leading contributions to the Wilson loops.
Subjects: |  High Energy Physics - Theory (hep-th); High Energy Physics - Phenomenology (hep-ph); Nuclear Theory (nucl-th)  
Cite as: | [arXiv:2301.00123](https://arxiv.org/abs/2301.00123) [hep-th]  
From: Dimitrios Giataganas [[view email](https://arxiv.org/show-email/a267dd60/2301.00123)] **[v1]** Sat, 31 Dec 2022 04:49:55 UTC (20 KB)"""
        },
        {
            "arxiv_id": "2302.01234",
            "markdown": """# Nuclear Experiment
**arXiv:2302.01234** (nucl-ex) 
[Submitted on 2 Feb 2023 ([v1](https://arxiv.org/abs/2302.01234v1)), last revised 9 Oct 2023 (this version, v2)]
#  Title:Symmetry plane correlations in Pb-Pb collisions at $\\sqrt{s_{\\rm NN}} = 2.76$ TeV
Authors: <AUTHORS>
> Abstract:A newly developed observable for correlations between symmetry planes, which characterize the direction of the anisotropic emission of produced particles, is measured in Pb-Pb collisions at $\\sqrt{s_{\\rm NN}} = 2.76$ TeV with ALICE. This so-called Gaussian Estimator allows for the first time the study of these quantities without the influence of correlations between different flow amplitudes. The centrality dependence of various correlations between two, three and four symmetry planes is presented. The ordering of magnitude between these symmetry plane correlations is discussed and the results of the Gaussian Estimator are compared with measurements of previously used estimators. The results utilizing the new estimator lead to significantly smaller correlations than reported by studies using the Scalar Product method. Furthermore, the obtained symmetry plane correlations are compared to state-of-the-art hydrodynamic model calculations for the evolution of heavy-ion collisions. While the model predictions provide a qualitative description of the data, quantitative agreement is not always observed, particularly for correlators with significant non-linear response of the medium to initial state anisotropies of the collision system. As these results provide unique and independent information, their usage in future Bayesian analysis can further constrain our knowledge on the properties of the QCD matter produced in ultrarelativistic heavy-ion collisions.
Subjects: |  Nuclear Experiment (nucl-ex); High Energy Physics - Experiment (hep-ex)  
Cite as: | [arXiv:2302.01234](https://arxiv.org/abs/2302.01234) [nucl-ex]
From: ALICE publications [[view email](https://arxiv.org/show-email/3ad3c78e/2302.01234)] [via Alice Collaboration as proxy] Thu, 2 Feb 2023 17:15:53 UTC (1,236 KB)"""
        }
    ]
    
    # 处理每篇论文
    processed_papers = []
    
    for paper_data in papers_data:
        arxiv_id = paper_data["arxiv_id"]
        markdown_content = paper_data["markdown"]
        
        print(f"\n处理论文: {arxiv_id}")
        
        # 提取论文信息
        paper_info = crawler.extract_paper_info(markdown_content, arxiv_id)
        
        # 保存数据
        filepath = crawler.save_paper_data(paper_info)
        
        print(f"  标题: {paper_info['title']}")
        print(f"  作者: {len(paper_info['authors'])} 人")
        print(f"  摘要长度: {len(paper_info['abstract'])} 字符")
        print(f"  学科: {len(paper_info['subjects'])} 个")
        print(f"  保存到: {filepath}")
        
        processed_papers.append(paper_info)
    
    return processed_papers

def show_summary():
    """显示处理结果汇总"""
    raw_dir = PROJECT_ROOT / "data" / "raw"
    json_files = list(raw_dir.glob("*.json"))
    
    print(f"\n=== 数据处理汇总 ===")
    print(f"总共处理: {len(json_files)} 篇论文")
    
    all_authors = set()
    all_subjects = set()
    
    for json_file in json_files:
        import json
        with open(json_file, 'r', encoding='utf-8') as f:
            paper = json.load(f)
            # 安全检查作者字段
            if 'authors' in paper:
                all_authors.update(paper['authors'])
            # 安全检查学科字段
            if 'subjects' in paper:
                all_subjects.update(s['code'] for s in paper['subjects'])
    
    print(f"唯一作者数: {len(all_authors)}")
    print(f"学科类别数: {len(all_subjects)}")
    print(f"学科类别: {', '.join(all_subjects)}")

if __name__ == "__main__":
    # 切换到项目目录
    os.chdir(PROJECT_ROOT)
    
    # 处理所有论文
    processed_papers = process_all_papers()
    
    # 显示汇总
    show_summary()
    
    print("\n=== 下一步 ===")
    print("数据抓取和预处理完成！")
    print("接下来可以运行:")
    print("1. 实体关系提取: python src/knowledge_construction/extractor.py")
    print("2. 启动Neo4j: docker-compose up -d")
    print("3. 加载到数据库: python src/knowledge_construction/loader.py")
