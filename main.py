"""
项目主启动脚本和测试程序
"""

import os
import sys
import json
from pathlib import Path

# 设置项目根路径
PROJECT_ROOT = Path(__file__).parent.absolute()
sys.path.append(str(PROJECT_ROOT))

def print_banner():
    """打印项目横幅"""
    banner = """
    ===============================================
    知识图谱挖掘项目 (Knowledge Graph Mining)
    基于ArXiv论文数据的知识图谱构建与数据挖掘
    ===============================================
    """
    print(banner)

def check_environment():
    """检查环境配置"""
    print("1. 检查环境配置...")
    
    issues = []
    
    # 确保在项目根目录
    os.chdir(PROJECT_ROOT)
    
    # 检查必要目录
    required_dirs = [
        "data/raw", "data/processed", "data/graphs",
        "src/data_acquisition", "src/knowledge_construction",
        "src/data_mining", "src/application"
    ]
    
    for dir_path in required_dirs:
        if not os.path.exists(dir_path):
            os.makedirs(dir_path, exist_ok=True)
            print(f"   [OK] 创建目录: {dir_path}")
    
    # 检查配置文件
    config_file = "configs/config.py"
    if os.path.exists(config_file):
        print(f"   [OK] 配置文件存在: {config_file}")
    else:
        issues.append(f"配置文件缺失: {config_file}")
    
    # 检查Docker配置
    docker_file = "docker-compose.yml"
    if os.path.exists(docker_file):
        print(f"   [OK] Docker配置存在: {docker_file}")
    else:
        issues.append(f"Docker配置缺失: {docker_file}")
    
    if issues:
        print("   [ERROR] 发现问题:")
        for issue in issues:
            print(f"      - {issue}")
        return False
    
    print("   [OK] 环境检查通过")
    return True

def show_setup_instructions():
    """显示设置说明"""
    instructions = """
    2. 环境设置说明:
    
    步骤1: 安装Python依赖
    cmd> pip install -r requirements.txt
    
    步骤2: 下载spaCy英文模型  
    cmd> python -m spacy download en_core_web_sm
    
    步骤3: 启动Neo4j数据库
    cmd> docker-compose up -d
    
    步骤4: 访问Neo4j浏览器界面
    浏览器打开: http://localhost:7474
    用户名: neo4j, 密码: password
    
    """
    print(instructions)

def show_crawl_instructions():
    """显示数据抓取说明"""
    instructions = """
    3. 数据抓取步骤:
    
    当前项目需要抓取3篇ArXiv论文作为测试数据：
    - https://arxiv.org/abs/2301.00001
    - https://arxiv.org/abs/2301.00123  
    - https://arxiv.org/abs/2302.01234
    
    请使用crawl4ai MCP工具逐个抓取：
    
    对于每个URL，请执行：
    1. 调用crawl4ai:md工具
    2. 参数: url="https://arxiv.org/abs/XXXXX", f="fit"
    3. 将返回的markdown内容保存
    
    完成抓取后，调用process_crawl_result()函数处理结果。
    
    """
    print(instructions)

def process_crawl_result(arxiv_id: str, markdown_content: str):
    """处理crawl4ai抓取结果的统一接口"""
    try:
        sys.path.append('src')
        from data_acquisition.main import process_crawl_result as process_func
        
        result = process_func(arxiv_id, markdown_content)
        print(f"[OK] 论文 {arxiv_id} 处理完成")
        return result
        
    except Exception as e:
        print(f"[ERROR] 处理论文 {arxiv_id} 时出错: {e}")
        return None

def run_knowledge_construction():
    """运行知识图谱构建"""
    print("4. 运行知识图谱构建...")
    
    try:
        # 检查原始数据
        raw_files = [f for f in os.listdir("data/raw") if f.endswith('.json')]
        if not raw_files:
            print("   [ERROR] 未找到原始数据文件，请先抓取数据")
            return False
        
        print(f"   [OK] 找到 {len(raw_files)} 个数据文件")
        
        # 运行实体关系提取
        print("   正在提取实体关系...")
        sys.path.append('src')
        from knowledge_construction.extractor import EntityRelationExtractor
        
        extractor = EntityRelationExtractor()
        extracted_data = extractor.process_all_papers()
        extractor.save_extracted_data(extracted_data)
        
        print("   [OK] 实体关系提取完成")
        
        # 加载到Neo4j
        print("   正在加载到Neo4j...")
        from knowledge_construction.loader import Neo4jLoader
        
        loader = Neo4jLoader()
        loader.load_all_data()
        loader.print_database_stats()
        loader.close()
        
        print("   [OK] 知识图谱构建完成")
        return True
        
    except Exception as e:
        print(f"   [ERROR] 知识图谱构建失败: {e}")
        return False

def show_data_mining_examples():
    """显示数据挖掘示例"""
    examples = """
    5. 数据挖掘示例:
    
    完成知识图谱构建后，可以运行以下数据挖掘任务：
    
    聚类分析 (社区发现):
    cmd> python src/data_mining/clustering.py
    
    分类预测 (图神经网络):
    cmd> python src/data_mining/classification.py
    
    关联规则 (频繁子图挖掘):
    cmd> python src/data_mining/association_rules.py
    
    """
    print(examples)

def show_status():
    """显示项目状态"""
    print("\n=== 项目状态检查 ===")
    
    # 检查数据文件
    raw_files = len([f for f in os.listdir("data/raw") if f.endswith('.json')]) if os.path.exists("data/raw") else 0
    processed_files = len([f for f in os.listdir("data/processed") if f.endswith('.json')]) if os.path.exists("data/processed") else 0
    
    print(f"原始数据文件: {raw_files} 个")
    print(f"处理数据文件: {processed_files} 个")
    
    # 检查Neo4j连接
    try:
        sys.path.append('src')
        from knowledge_construction.loader import Neo4jLoader
        loader = Neo4jLoader()
        stats = loader.get_database_stats()
        loader.close()
        
        print(f"Neo4j数据库状态: [OK] 连接正常")
        print(f"  - 论文: {stats.get('papers', 0)} 个")
        print(f"  - 作者: {stats.get('authors', 0)} 个") 
        print(f"  - 主题: {stats.get('topics', 0)} 个")
        
    except Exception as e:
        print(f"Neo4j数据库状态: [ERROR] 连接失败 ({e})")

def main():
    """主函数"""
    print_banner()
    
    if not check_environment():
        print("请修复环境问题后重试")
        return
    
    show_setup_instructions()
    show_crawl_instructions()
    
    # 显示当前状态
    show_status()
    
    print("\n=== 下一步操作 ===")
    print("1. 首先启动Neo4j: docker-compose up -d")
    print("2. 使用crawl4ai MCP抓取测试数据")
    print("3. 调用process_crawl_result()处理抓取结果")
    print("4. 运行run_knowledge_construction()构建知识图谱")
    print("5. 执行数据挖掘任务")

def quick_test():
    """快速测试函数"""
    print("\n=== 快速测试 ===")
    
    # 确保在正确目录
    os.chdir(PROJECT_ROOT)
    
    # 测试配置导入
    try:
        sys.path.insert(0, str(PROJECT_ROOT))
        from configs import config
        print("[OK] 配置导入成功")
    except Exception as e:
        print(f"[ERROR] 配置导入失败: {e}")
    
    # 测试数据处理模块
    try:
        from src.data_acquisition.crawler import ArxivCrawler
        crawler = ArxivCrawler()
        print("[OK] 数据抓取模块导入成功")
    except Exception as e:
        print(f"[ERROR] 数据抓取模块导入失败: {e}")
    
    # 测试知识构建模块
    try:
        from src.knowledge_construction.extractor import EntityRelationExtractor
        extractor = EntityRelationExtractor() 
        print("[OK] 实体关系提取模块导入成功")
    except Exception as e:
        print(f"[ERROR] 实体关系提取模块导入失败: {e}")

if __name__ == "__main__":
    main()
    quick_test()
