"""
AI驱动的智能数据处理模块
按照文档指南处理crawl4ai获取的数据并整合到知识图谱项目中
"""

import json
import os
import sys
import re
from datetime import datetime
from pathlib import Path

# 设置项目路径
PROJECT_ROOT = Path(__file__).parent.absolute()
sys.path.append(str(PROJECT_ROOT))

def process_ai_crawl_result(content: str, url: str, research_query: str = "图神经网络在推荐系统中的应用"):
    """
    AI驱动的智能数据处理函数
    
    Args:
        content: crawl4ai获取的markdown内容
        url: 数据源URL
        research_query: 研究查询主题
    
    Returns:
        dict: 处理结果统计
    """
    print(f"[AI] 开始智能数据处理...")
    print(f"[QUERY] 研究主题: {research_query}")
    print(f"[SOURCE] 数据源: {url}")
    print(f"[SIZE] 内容大小: {len(content)} 字符")
    
    # 第一步：智能内容分析
    analysis_result = intelligent_content_analysis(content, research_query)
    
    # 第二步：提取相关论文
    relevant_papers = extract_relevant_papers(content, research_query)
    
    # 第三步：构建实体关系
    entities_relations = build_entities_relations(relevant_papers)
    
    # 第四步：保存处理结果
    save_result = save_intelligent_results(relevant_papers, entities_relations, research_query, url)
    
    # 第五步：生成处理报告
    report = generate_processing_report(analysis_result, relevant_papers, entities_relations, save_result)
    
    print_processing_summary(report)
    
    return report


def intelligent_content_analysis(content: str, research_query: str) -> dict:
    """AI智能内容分析"""
    
    analysis = {
        "total_papers": 0,
        "relevant_papers": 0,
        "key_concepts_found": [],
        "research_domains": [],
        "content_quality": "high"
    }
    
    # 计算论文总数
    paper_entries = re.findall(r'\[(\d+)\]\s*\[\s*arXiv:(\d+\.\d+)\s*\]', content)
    analysis["total_papers"] = len(paper_entries)
    
    # 识别与研究主题相关的关键词
    key_terms = {
        "graph": ["graph", "neural network", "GNN", "graph transformer"],
        "recommendation": ["recommendation", "recommender", "collaborative filtering"],
        "machine_learning": ["machine learning", "deep learning", "neural network"],
        "federated": ["federated learning", "distributed learning"],
        "reinforcement": ["reinforcement learning", "RL", "policy"]
    }
    
    content_lower = content.lower()
    
    for domain, terms in key_terms.items():
        found_terms = [term for term in terms if term in content_lower]
        if found_terms:
            analysis["key_concepts_found"].extend(found_terms)
            analysis["research_domains"].append(domain)
    
    # 计算相关论文数量（粗略估算）
    relevant_count = 0
    if "graph" in analysis["research_domains"]:
        relevant_count += content_lower.count("graph")
    if "recommendation" in analysis["research_domains"]:
        relevant_count += content_lower.count("recommendation")
    
    analysis["relevant_papers"] = min(relevant_count, analysis["total_papers"])
    
    return analysis


def extract_relevant_papers(content: str, research_query: str) -> list:
    """提取与研究主题相关的论文"""
    
    papers = []
    
    # 使用正则表达式提取论文信息
    paper_pattern = r'\[(\d+)\]\s*\[\s*arXiv:(\d+\.\d+)\s*\].*?Title:\s*(.*?)\n(?:.*?Authors?:\s*(.*?)\n)?(?:.*?Comments:\s*(.*?)\n)?(?:.*?Subjects:\s*(.*?)\n)?'
    
    matches = re.finditer(paper_pattern, content, re.DOTALL)
    
    for match in matches:
        paper_id = match.group(1)
        arxiv_id = match.group(2)
        title = match.group(3).strip() if match.group(3) else ""
        authors = match.group(4).strip() if match.group(4) else ""
        comments = match.group(5).strip() if match.group(5) else ""
        subjects = match.group(6).strip() if match.group(6) else ""
        
        # 检查论文是否与研究主题相关
        relevance_score = calculate_relevance_score(title, subjects, research_query)
        
        if relevance_score > 0.3:  # 相关性阈值
            paper_info = {
                "paper_id": paper_id,
                "arxiv_id": arxiv_id,
                "title": title,
                "authors": parse_authors(authors),
                "comments": comments,
                "subjects": parse_subjects(subjects),
                "relevance_score": relevance_score,
                "research_context": research_query,
                "extraction_timestamp": datetime.now().isoformat()
            }
            papers.append(paper_info)
    
    # 按相关性排序
    papers.sort(key=lambda x: x["relevance_score"], reverse=True)
    
    return papers


def calculate_relevance_score(title: str, subjects: str, research_query: str) -> float:
    """计算论文与研究主题的相关性分数"""
    
    score = 0.0
    title_lower = title.lower()
    subjects_lower = subjects.lower()
    query_lower = research_query.lower()
    
    # 高权重关键词
    high_weight_terms = {
        "graph neural network": 2.0,
        "graph transformer": 1.8,
        "recommendation": 1.5,
        "recommender system": 1.8,
        "collaborative filtering": 1.5,
        "gnn": 1.5
    }
    
    # 中权重关键词  
    medium_weight_terms = {
        "graph": 1.0,
        "neural network": 1.0,
        "deep learning": 0.8,
        "machine learning": 0.6,
        "federated learning": 1.2
    }
    
    # 检查高权重关键词
    for term, weight in high_weight_terms.items():
        if term in title_lower:
            score += weight
        elif term in subjects_lower:
            score += weight * 0.7
    
    # 检查中权重关键词
    for term, weight in medium_weight_terms.items():
        if term in title_lower:
            score += weight
        elif term in subjects_lower:
            score += weight * 0.5
    
    # 标准化分数
    return min(score / 3.0, 1.0)


def parse_authors(authors_string: str) -> list:
    """解析作者字符串"""
    if not authors_string:
        return []
    
    # 移除链接格式
    authors_clean = re.sub(r'\[.*?\]\(.*?\)', '', authors_string)
    
    # 分割作者名
    authors = [author.strip() for author in authors_clean.split(',')]
    authors = [author for author in authors if author and len(author) > 1]
    
    return authors


def parse_subjects(subjects_string: str) -> list:
    """解析学科分类字符串"""
    if not subjects_string:
        return []
    
    # 提取学科分类
    subjects = re.findall(r'([^(]+)(?:\([^)]+\))?', subjects_string)
    subjects = [subject.strip() for subject in subjects if subject.strip()]
    
    return subjects


def build_entities_relations(papers: list) -> dict:
    """构建实体关系数据"""
    
    entities_relations = {
        "papers": [],
        "authors": [],
        "topics": [],
        "institutions": [],
        "relations": {
            "author_paper": [],
            "paper_topic": [],
            "author_collaboration": [],
            "topic_cooccurrence": []
        }
    }
    
    # 提取论文实体
    for paper in papers:
        paper_entity = {
            "id": f"paper_{paper['arxiv_id']}",
            "type": "Paper",
            "title": paper["title"],
            "arxiv_id": paper["arxiv_id"],
            "relevance_score": paper["relevance_score"]
        }
        entities_relations["papers"].append(paper_entity)
        
        # 提取作者实体和关系
        for author in paper["authors"]:
            author_id = f"author_{author.replace(' ', '_')}"
            
            # 添加作者实体（去重）
            if not any(a["id"] == author_id for a in entities_relations["authors"]):
                author_entity = {
                    "id": author_id,
                    "type": "Author", 
                    "name": author
                }
                entities_relations["authors"].append(author_entity)
            
            # 添加作者-论文关系
            relation = {
                "source": author_id,
                "target": paper_entity["id"],
                "type": "AUTHORED"
            }
            entities_relations["relations"]["author_paper"].append(relation)
        
        # 提取主题实体和关系
        for subject in paper["subjects"]:
            topic_id = f"topic_{subject.replace(' ', '_')}"
            
            # 添加主题实体（去重）
            if not any(t["id"] == topic_id for t in entities_relations["topics"]):
                topic_entity = {
                    "id": topic_id,
                    "type": "Topic",
                    "name": subject
                }
                entities_relations["topics"].append(topic_entity)
            
            # 添加论文-主题关系
            relation = {
                "source": paper_entity["id"],
                "target": topic_id,
                "type": "BELONGS_TO"
            }
            entities_relations["relations"]["paper_topic"].append(relation)
    
    return entities_relations


def save_intelligent_results(papers: list, entities_relations: dict, research_query: str, source_url: str) -> dict:
    """保存智能处理结果"""
    
    # 确保数据目录存在
    data_dir = Path("data/raw")
    data_dir.mkdir(parents=True, exist_ok=True)
    
    # 生成文件名
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    filename = f"intelligent_crawl_results_{timestamp}.json"
    filepath = data_dir / filename
    
    # 准备保存数据
    save_data = {
        "research_query": research_query,
        "source_url": source_url,
        "processing_timestamp": datetime.now().isoformat(),
        "papers_count": len(papers),
        "entities_count": {
            "papers": len(entities_relations["papers"]),
            "authors": len(entities_relations["authors"]),
            "topics": len(entities_relations["topics"])
        },
        "relations_count": {
            "author_paper": len(entities_relations["relations"]["author_paper"]),
            "paper_topic": len(entities_relations["relations"]["paper_topic"])
        },
        "papers": papers,
        "entities_relations": entities_relations
    }
    
    # 保存到文件
    with open(filepath, 'w', encoding='utf-8') as f:
        json.dump(save_data, f, ensure_ascii=False, indent=2)
    
    return {
        "filename": filename,
        "filepath": str(filepath),
        "file_size": os.path.getsize(filepath),
        "status": "success"
    }


def generate_processing_report(analysis: dict, papers: list, entities_relations: dict, save_result: dict) -> dict:
    """生成处理报告"""
    
    report = {
        "processing_summary": {
            "total_papers_found": analysis["total_papers"],
            "relevant_papers_extracted": len(papers),
            "relevance_rate": len(papers) / max(analysis["total_papers"], 1),
            "key_concepts": analysis["key_concepts_found"],
            "research_domains": analysis["research_domains"]
        },
        "entity_statistics": {
            "papers": len(entities_relations["papers"]),
            "authors": len(entities_relations["authors"]),
            "topics": len(entities_relations["topics"]),
            "total_relations": sum(len(relations) for relations in entities_relations["relations"].values())
        },
        "data_quality": {
            "content_quality": analysis["content_quality"],
            "extraction_completeness": "high" if len(papers) > 5 else "medium",
            "data_consistency": "high"
        },
        "file_info": save_result,
        "next_steps": [
            "运行知识图谱构建模块",
            "执行数据挖掘分析",
            "生成研究洞察报告"
        ]
    }
    
    return report


def print_processing_summary(report: dict):
    """打印处理摘要"""
    
    print(f"\n{' AI智能数据处理完成 ':#^60}")
    
    print(f"\n[RESULTS] 处理结果:")
    print(f"   - 论文总数: {report['processing_summary']['total_papers_found']}")
    print(f"   - 相关论文: {report['processing_summary']['relevant_papers_extracted']}")
    print(f"   - 相关率: {report['processing_summary']['relevance_rate']:.2%}")
    print(f"   - 实体总数: {report['entity_statistics']['papers'] + report['entity_statistics']['authors'] + report['entity_statistics']['topics']}")
    print(f"   - 关系数量: {report['entity_statistics']['total_relations']}")
    
    print(f"\n[CONCEPTS] 发现的关键概念:")
    for concept in report['processing_summary']['key_concepts'][:8]:
        print(f"   - {concept}")
    
    print(f"\n[DOMAINS] 研究领域:")
    for domain in report['processing_summary']['research_domains']:
        print(f"   - {domain}")
    
    print(f"\n[FILE] 结果文件:")
    print(f"   - 文件名: {report['file_info']['filename']}")
    print(f"   - 大小: {report['file_info']['file_size']:,} 字节")
    
    print(f"\n[NEXT] 下一步建议:")
    for step in report['next_steps']:
        print(f"   * {step}")
    
    print(f"\n{'='*60}")


# 主执行函数
if __name__ == "__main__":
    
    # 示例数据（模拟crawl4ai结果）
    sample_content = """
    [1] arXiv:2506.20575 
    Title: Exploring Graph-Transformer Out-of-Distribution Generalization Abilities
    Subjects: Machine Learning (cs.LG)
    
    [2] arXiv:2506.20329
    Title: Producer-Fairness in Sequential Bundle Recommendation
    Subjects: Machine Learning (cs.LG)
    """
    
    sample_url = "https://arxiv.org/list/cs.LG/recent"
    sample_query = "图神经网络在推荐系统中的应用"
    
    print("AI智能数据处理模块测试...")
    result = process_ai_crawl_result(sample_content, sample_url, sample_query)
    
    print(f"\n[TEST] 处理完成，相关论文数: {result['processing_summary']['relevant_papers_extracted']}")
