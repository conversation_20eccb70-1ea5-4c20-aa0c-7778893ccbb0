"""
增强版实体关系提取器
支持AI智能爬取的多源数据处理
"""

import json
import re
import os
from typing import List, Dict, Tuple, Set
from datetime import datetime
from pathlib import Path

# 添加项目路径
PROJECT_ROOT = Path(__file__).parent.parent.parent.absolute()

try:
    import spacy
    SPACY_AVAILABLE = True
except ImportError:
    SPACY_AVAILABLE = False


class IntelligentEntityExtractor:
    """智能实体关系提取器 - 处理AI爬取的多样化数据"""
    
    def __init__(self):
        self.nlp = None
        if SPACY_AVAILABLE:
            try:
                self.nlp = spacy.load("en_core_web_sm")
            except OSError:
                print("警告: spaCy英文模型未安装")
        
        # 智能模式的实体识别模式
        self.smart_patterns = {
            "authors": [
                r"Authors?:\s*([^,\n]+(?:,\s*[^,\n]+)*)",
                r"By\s+([A-Z][a-z]+\s+[A-Z][a-z]+(?:,\s*[A-Z][a-z]+\s+[A-Z][a-z]+)*)",
                r"\[([A-Z][a-z]+\s+[A-Z][a-z]+)\]"
            ],
            "titles": [
                r"Title:\s*(.+?)(?:\n|$)",
                r"#\s+(.+?)(?:\n|$)",
                r"^(.+?)(?:\n.*?Abstract:)",
            ],
            "abstracts": [
                r"Abstract:\s*(.+?)(?:\n\n|\nSubjects:|\nComments:)",
                r"> Abstract:\s*(.+?)(?:\n[^>]|\nSubjects:)",
            ],
            "subjects": [
                r"Subjects?:\s*\|\s*(.+?)\s*\|",
                r"Categories?:\s*(.+?)(?:\n|$)",
            ],
            "institutions": [
                r"([A-Z][a-z]+\s+University)",
                r"([A-Z][a-z]+\s+Institute)",
                r"(MIT|Stanford|Harvard|Berkeley|CMU|DeepMind|Google|Microsoft)",
            ]
        }
        
        # 主题关键词扩展库
        self.enhanced_topic_keywords = {
            "graph_neural_networks": [
                "graph neural network", "gnn", "graph convolution", "graph attention",
                "node classification", "link prediction", "graph embedding"
            ],
            "recommendation_systems": [
                "recommendation", "recommender system", "collaborative filtering", 
                "matrix factorization", "content-based filtering"
            ],
            "computer_vision": [
                "computer vision", "image classification", "object detection", "cnn",
                "convolutional neural network", "image processing"
            ],
            "natural_language_processing": [
                "natural language processing", "nlp", "language model", "bert", "gpt",
                "text classification", "sentiment analysis", "machine translation"
            ],
            "deep_learning": [
                "deep learning", "neural network", "backpropagation", "gradient descent",
                "transformer", "attention mechanism"
            ],
            "reinforcement_learning": [
                "reinforcement learning", "q-learning", "policy gradient", "actor-critic",
                "markov decision process", "reward function"
            ],
            "blockchain": [
                "blockchain", "cryptocurrency", "smart contract", "consensus algorithm",
                "distributed ledger", "proof of work", "proof of stake"
            ],
            "quantum_computing": [
                "quantum computing", "quantum algorithm", "qubit", "quantum entanglement",
                "quantum supremacy", "quantum gate"
            ]
        }
    
    def process_intelligent_crawl_result(self, 
                                       content: str, 
                                       source_url: str, 
                                       research_context: str = "") -> Dict:
        """处理AI智能爬取的结果"""
        
        print(f"处理智能爬取内容: {source_url}")
        print(f"研究上下文: {research_context}")
        
        # 分析内容类型
        content_type = self._detect_content_type(content, source_url)
        print(f"检测到内容类型: {content_type}")
        
        if content_type == "arxiv_listing":
            return self._process_arxiv_listing(content, research_context)
        elif content_type == "arxiv_paper":
            return self._process_single_paper(content)
        elif content_type == "general_academic":
            return self._process_general_content(content, research_context)
        else:
            return self._process_generic_content(content)
    
    def _detect_content_type(self, content: str, url: str) -> str:
        """智能检测内容类型"""
        
        if "arxiv.org/list/" in url:
            return "arxiv_listing"
        elif "arxiv.org/abs/" in url:
            return "arxiv_paper"
        elif any(indicator in content.lower() for indicator in 
                ["abstract:", "authors:", "subjects:"]):
            return "arxiv_paper"
        elif "arxiv:" in content or "arXiv:" in content:
            return "arxiv_listing"
        else:
            return "general_academic"
    
    def _process_arxiv_listing(self, content: str, research_context: str) -> Dict:
        """处理ArXiv列表页面"""
        
        extracted_papers = []
        
        # 使用正则表达式提取论文信息
        # ArXiv列表页面的模式识别
        paper_blocks = re.split(r'\n(?=\*\*arXiv:|\d+\.\s)', content)
        
        for block in paper_blocks:
            if len(block.strip()) < 50:  # 过滤太短的内容
                continue
                
            paper_info = self._extract_paper_from_block(block, research_context)
            if paper_info and paper_info.get('title'):
                extracted_papers.append(paper_info)
        
        # 生成实体和关系
        entities = {"Paper": [], "Author": [], "Topic": [], "Subject": []}
        relations = []
        
        for paper in extracted_papers:
            paper_id = paper.get('arxiv_id', f"paper_{len(entities['Paper'])}")
            entities["Paper"].append(paper_id)
            
            # 处理作者
            for author in paper.get('authors', []):
                author_id = self._normalize_name(author)
                if author_id not in entities["Author"]:
                    entities["Author"].append(author_id)
                
                relations.append({
                    "type": "AUTHORED_BY",
                    "source": paper_id,
                    "target": author_id,
                    "source_type": "Paper",
                    "target_type": "Author"
                })
            
            # 处理主题
            for topic in paper.get('topics', []):
                topic_id = topic.lower().replace(' ', '_')
                if topic_id not in entities["Topic"]:
                    entities["Topic"].append(topic_id)
                
                relations.append({
                    "type": "HAS_TOPIC",
                    "source": paper_id,
                    "target": topic_id,
                    "source_type": "Paper",
                    "target_type": "Topic"
                })
        
        print(f"从ArXiv列表提取: {len(extracted_papers)} 篇论文")
        
        return {
            "entities": entities,
            "relations": relations,
            "papers": extracted_papers,
            "source_type": "arxiv_listing",
            "research_context": research_context
        }
    
    def _extract_paper_from_block(self, block: str, context: str) -> Dict:
        """从文本块中提取单篇论文信息"""
        
        paper_info = {
            "arxiv_id": "",
            "title": "",
            "authors": [],
            "abstract": "",
            "subjects": [],
            "topics": [],
            "url": "",
            "relevance_score": 0.0
        }
        
        # 提取ArXiv ID
        arxiv_match = re.search(r'arXiv:(\d+\.\d+)', block)
        if arxiv_match:
            paper_info["arxiv_id"] = arxiv_match.group(1)
            paper_info["url"] = f"https://arxiv.org/abs/{paper_info['arxiv_id']}"
        
        # 提取标题
        for pattern in self.smart_patterns["titles"]:
            match = re.search(pattern, block, re.MULTILINE | re.DOTALL)
            if match:
                title = match.group(1).strip()
                if len(title) > 10:  # 过滤太短的标题
                    paper_info["title"] = title
                    break
        
        # 提取作者
        for pattern in self.smart_patterns["authors"]:
            match = re.search(pattern, block, re.IGNORECASE)
            if match:
                authors_text = match.group(1)
                # 清理和分割作者名称
                authors = [name.strip() for name in re.split(r',|;|\n', authors_text)
                          if name.strip() and len(name.strip()) > 2]
                paper_info["authors"] = authors[:6]  # 限制作者数量
                break
        
        # 提取摘要
        for pattern in self.smart_patterns["abstracts"]:
            match = re.search(pattern, block, re.MULTILINE | re.DOTALL)
            if match:
                abstract = match.group(1).strip()
                if len(abstract) > 50:
                    paper_info["abstract"] = abstract
                    break
        
        # 智能主题提取
        paper_info["topics"] = self._extract_intelligent_topics(
            paper_info["title"] + " " + paper_info["abstract"], 
            context
        )
        
        # 计算相关性分数
        paper_info["relevance_score"] = self._calculate_relevance(paper_info, context)
        
        return paper_info
    
    def _extract_intelligent_topics(self, text: str, context: str) -> List[str]:
        """基于研究上下文智能提取主题"""
        
        if not text:
            return []
        
        text_lower = text.lower()
        found_topics = []
        
        # 根据研究上下文优先匹配相关主题
        context_lower = context.lower()
        
        # 优先匹配与研究上下文相关的主题
        for topic_category, keywords in self.enhanced_topic_keywords.items():
            for keyword in keywords:
                if keyword in text_lower:
                    # 如果关键词也在研究上下文中，给予更高优先级
                    if any(ctx_word in keyword for ctx_word in context_lower.split()):
                        found_topics.insert(0, topic_category)
                    else:
                        found_topics.append(topic_category)
                    break
        
        # 移除重复并限制数量
        unique_topics = []
        for topic in found_topics:
            if topic not in unique_topics:
                unique_topics.append(topic)
        
        return unique_topics[:4]  # 最多返回4个主题
    
    def _calculate_relevance(self, paper_info: Dict, context: str) -> float:
        """计算论文与研究上下文的相关性分数"""
        
        if not context:
            return 0.5  # 默认相关性
        
        context_words = set(context.lower().split())
        paper_text = (paper_info.get("title", "") + " " + 
                     paper_info.get("abstract", "")).lower()
        
        # 计算关键词重叠度
        matches = sum(1 for word in context_words if word in paper_text)
        score = min(matches / len(context_words), 1.0) if context_words else 0.0
        
        # 主题匹配加权
        if paper_info.get("topics"):
            score += 0.2 * len(paper_info["topics"])
        
        return min(score, 1.0)
    
    def _normalize_name(self, name: str) -> str:
        """标准化名称"""
        return re.sub(r'\s+', '_', name.strip().lower())
    
    def _process_single_paper(self, content: str) -> Dict:
        """处理单篇论文页面"""
        # 复用现有的论文处理逻辑
        return self._extract_paper_from_block(content, "")
    
    def _process_general_content(self, content: str, context: str) -> Dict:
        """处理通用学术内容"""
        # 简化的通用处理逻辑
        return {
            "entities": {"Paper": [], "Author": [], "Topic": [], "Subject": []},
            "relations": [],
            "papers": [],
            "source_type": "general_academic"
        }
    
    def save_intelligent_results(self, results: Dict, filename: str = None):
        """保存智能处理结果"""
        
        if not filename:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"intelligent_crawl_results_{timestamp}.json"
        
        output_path = PROJECT_ROOT / "data" / "processed" / filename
        output_path.parent.mkdir(parents=True, exist_ok=True)
        
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(results, f, ensure_ascii=False, indent=2)
        
        print(f"智能处理结果已保存: {output_path}")
        return str(output_path)


# 便于Claude Desktop调用的简化接口
def process_ai_crawl_result(content: str, 
                          source_url: str, 
                          research_query: str = "") -> str:
    """
    Claude Desktop调用接口
    处理AI爬取的内容并自动整合到项目中
    """
    
    extractor = IntelligentEntityExtractor()
    results = extractor.process_intelligent_crawl_result(
        content, source_url, research_query
    )
    
    # 保存结果
    output_file = extractor.save_intelligent_results(results)
    
    # 返回处理摘要
    summary = f"""
智能数据处理完成！

📊 处理结果:
- 论文数量: {len(results.get('papers', []))}
- 实体总数: {sum(len(entities) for entities in results.get('entities', {}).values())}
- 关系数量: {len(results.get('relations', []))}
- 结果文件: {output_file}

🎯 研究上下文: {research_query}
🌐 数据源: {source_url}

下一步: 可以运行数据挖掘模块更新分析结果
"""
    
    return summary


if __name__ == "__main__":
    print("智能实体关系提取器已准备就绪")
    print("可通过process_ai_crawl_result()函数在Claude Desktop中调用")
