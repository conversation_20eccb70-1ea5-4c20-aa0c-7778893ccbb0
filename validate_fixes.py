"""
Validate Fixes for Data Science Issues
验证修复后的结果，确保问题已解决
"""

import json
import numpy as np
from pathlib import Path
import matplotlib.pyplot as plt
import seaborn as sns

# Project setup
PROJECT_ROOT = Path(__file__).parent.absolute()

def load_original_results():
    """加载原始（有问题的）结果"""
    print("Loading ORIGINAL (problematic) results...")
    
    original_classification = None
    original_association = None
    
    # Original classification results
    orig_class_file = PROJECT_ROOT / "data" / "processed" / "enhanced_classification_results.json"
    if orig_class_file.exists():
        with open(orig_class_file, 'r', encoding='utf-8') as f:
            original_classification = json.load(f)
    
    # Original association results
    orig_assoc_file = PROJECT_ROOT / "data" / "processed" / "enhanced_gspan_mining_results.json"
    if orig_assoc_file.exists():
        with open(orig_assoc_file, 'r', encoding='utf-8') as f:
            original_association = json.load(f)
    
    return original_classification, original_association

def load_fixed_results():
    """加载修复后的结果"""
    print("Loading FIXED results...")
    
    fixed_classification = None
    fixed_association = None
    
    # Fixed classification results
    fixed_class_file = PROJECT_ROOT / "data" / "processed" / "fixed_classification_results.json"
    if fixed_class_file.exists():
        with open(fixed_class_file, 'r', encoding='utf-8') as f:
            fixed_classification = json.load(f)
    
    # Fixed association results
    fixed_assoc_file = PROJECT_ROOT / "data" / "processed" / "fixed_association_rules_results.json"
    if fixed_assoc_file.exists():
        with open(fixed_assoc_file, 'r', encoding='utf-8') as f:
            fixed_association = json.load(f)
    
    return fixed_classification, fixed_association

def compare_classification_results(original, fixed):
    """比较分类结果"""
    print("\n" + "="*60)
    print("CLASSIFICATION RESULTS COMPARISON")
    print("="*60)
    
    if not original or not fixed:
        print("❌ Missing results files")
        return
    
    # Extract Random Forest results
    orig_rf = original['ml_results']['Random Forest']
    fixed_rf = fixed['random_forest_results']
    
    print("📊 RANDOM FOREST ACCURACY COMPARISON:")
    print(f"   Original (PROBLEMATIC): {orig_rf['test_accuracy']:.4f} ({orig_rf['test_accuracy']*100:.1f}%)")
    print(f"   Fixed (CORRECTED):      {fixed_rf['test_accuracy']:.4f} ({fixed_rf['test_accuracy']*100:.1f}%)")
    
    # Check if fix was successful
    if orig_rf['test_accuracy'] >= 0.999:
        print("   🚨 Original: SUSPICIOUS 100% accuracy (data leakage)")
    else:
        print("   ✅ Original: Reasonable accuracy")
    
    if fixed_rf['test_accuracy'] < 0.999:
        print("   ✅ Fixed: Realistic accuracy (data leakage eliminated)")
    else:
        print("   ⚠️  Fixed: Still suspiciously high")
    
    # Cross-validation comparison
    print(f"\n📊 CROSS-VALIDATION RESULTS:")
    print(f"   Fixed CV Accuracy: {fixed_rf['cv_mean']:.4f} ± {fixed_rf['cv_std']:.4f}")
    
    if fixed_rf['cv_std'] > 0.05:
        print("   ✅ Good variance in CV scores (indicates proper validation)")
    else:
        print("   ⚠️  Low variance in CV scores")
    
    # Feature engineering comparison
    print(f"\n🔧 FEATURE ENGINEERING FIXES:")
    print(f"   Original: Global centrality on entire graph")
    print(f"   Fixed: {fixed['feature_engineering']['feature_isolation']}")
    print(f"   Fixed: {fixed['feature_engineering']['test_features']}")
    
    return {
        'original_accuracy': orig_rf['test_accuracy'],
        'fixed_accuracy': fixed_rf['test_accuracy'],
        'fix_successful': orig_rf['test_accuracy'] >= 0.999 and fixed_rf['test_accuracy'] < 0.999
    }

def compare_association_results(original, fixed):
    """比较关联规则结果"""
    print("\n" + "="*60)
    print("ASSOCIATION RULES RESULTS COMPARISON")
    print("="*60)
    
    if not original or not fixed:
        print("❌ Missing results files")
        return
    
    # Extract rules
    orig_rules = original['comprehensive_report']['top_rules']
    fixed_rules = fixed['association_rules']
    
    print(f"📊 ASSOCIATION RULES COMPARISON:")
    print(f"   Original rules: {len(orig_rules)}")
    print(f"   Fixed rules: {len(fixed_rules)}")
    
    # Check confidence values
    print(f"\n🔍 CONFIDENCE VALUES ANALYSIS:")
    
    # Original rules confidence analysis
    orig_confidences = [rule['confidence'] for rule in orig_rules]
    invalid_orig = [c for c in orig_confidences if c > 1.0]
    
    print(f"   Original:")
    print(f"     Max confidence: {max(orig_confidences):.3f}")
    print(f"     Invalid (>1.0): {len(invalid_orig)} rules")
    
    if invalid_orig:
        print(f"     🚨 PROBLEMATIC: {len(invalid_orig)} rules with confidence > 100%")
        print(f"     Highest invalid: {max(invalid_orig):.3f} ({max(invalid_orig)*100:.1f}%)")
    
    # Fixed rules confidence analysis
    fixed_confidences = [rule['confidence'] for rule in fixed_rules]
    invalid_fixed = [c for c in fixed_confidences if c > 1.0]
    
    print(f"   Fixed:")
    print(f"     Max confidence: {max(fixed_confidences):.3f}")
    print(f"     Invalid (>1.0): {len(invalid_fixed)} rules")
    
    if len(invalid_fixed) == 0:
        print(f"     ✅ FIXED: All confidence values ≤ 1.0")
    else:
        print(f"     ❌ STILL BROKEN: {len(invalid_fixed)} invalid rules")
    
    # Show specific rule comparison
    print(f"\n📋 SPECIFIC RULE EXAMPLES:")
    
    # Find BELONGS_TO => Subject rule in original
    belongs_to_rule = None
    for rule in orig_rules:
        if 'BELONGS_TO' in rule['antecedent'] and 'Subject' in rule['consequent']:
            belongs_to_rule = rule
            break
    
    if belongs_to_rule:
        print(f"   Original BELONGS_TO => Subject:")
        print(f"     Confidence: {belongs_to_rule['confidence']:.3f} ({belongs_to_rule['confidence']*100:.1f}%)")
        if belongs_to_rule['confidence'] > 1.0:
            print(f"     🚨 INVALID: Confidence > 100%")
    
    # Show top fixed rules
    print(f"   Top Fixed Rules:")
    for i, rule in enumerate(fixed_rules[:3]):
        ant = ' + '.join(rule['antecedent'])
        con = ' + '.join(rule['consequent'])
        print(f"     {i+1}. {ant} => {con}")
        print(f"        Confidence: {rule['confidence']:.3f} ({rule['confidence']*100:.1f}%)")
    
    return {
        'original_invalid_count': len(invalid_orig),
        'fixed_invalid_count': len(invalid_fixed),
        'fix_successful': len(invalid_orig) > 0 and len(invalid_fixed) == 0
    }

def create_comparison_visualization(class_comparison, assoc_comparison):
    """创建比较可视化"""
    print(f"\n📊 Creating comparison visualization...")
    
    # Set up the plot
    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 10))
    fig.suptitle('Data Science Issues: Before vs After Fixes', fontsize=16, fontweight='bold')
    
    # 1. Classification Accuracy Comparison
    if class_comparison:
        categories = ['Original\n(Data Leakage)', 'Fixed\n(Proper Split)']
        accuracies = [class_comparison['original_accuracy'], class_comparison['fixed_accuracy']]
        colors = ['red' if acc >= 0.999 else 'green' for acc in accuracies]
        
        bars1 = ax1.bar(categories, accuracies, color=colors, alpha=0.7)
        ax1.set_title('Random Forest Test Accuracy', fontweight='bold')
        ax1.set_ylabel('Accuracy')
        ax1.set_ylim(0, 1.1)
        
        # Add value labels
        for bar, acc in zip(bars1, accuracies):
            height = bar.get_height()
            ax1.text(bar.get_x() + bar.get_width()/2., height + 0.01,
                    f'{acc:.3f}\n({acc*100:.1f}%)', ha='center', va='bottom')
        
        # Add warning line at 99%
        ax1.axhline(y=0.99, color='orange', linestyle='--', alpha=0.7, label='Suspicious threshold')
        ax1.legend()
    
    # 2. Confidence Values Distribution
    if assoc_comparison:
        categories = ['Original\n(Invalid)', 'Fixed\n(Valid)']
        invalid_counts = [assoc_comparison['original_invalid_count'], assoc_comparison['fixed_invalid_count']]
        colors = ['red' if count > 0 else 'green' for count in invalid_counts]
        
        bars2 = ax2.bar(categories, invalid_counts, color=colors, alpha=0.7)
        ax2.set_title('Invalid Confidence Values (>100%)', fontweight='bold')
        ax2.set_ylabel('Number of Invalid Rules')
        
        # Add value labels
        for bar, count in zip(bars2, invalid_counts):
            height = bar.get_height()
            ax2.text(bar.get_x() + bar.get_width()/2., height + 0.1,
                    f'{count}', ha='center', va='bottom')
    
    # 3. Fix Status Summary
    fix_status = ['Classification\nData Leakage', 'Association Rules\nConfidence Error']
    fix_success = [
        1 if class_comparison and class_comparison['fix_successful'] else 0,
        1 if assoc_comparison and assoc_comparison['fix_successful'] else 0
    ]
    colors = ['green' if success else 'red' for success in fix_success]
    
    bars3 = ax3.bar(fix_status, fix_success, color=colors, alpha=0.7)
    ax3.set_title('Fix Success Status', fontweight='bold')
    ax3.set_ylabel('Fixed (1) / Not Fixed (0)')
    ax3.set_ylim(0, 1.2)
    
    # Add status labels
    for bar, success in zip(bars3, fix_success):
        height = bar.get_height()
        status = '✅ FIXED' if success else '❌ NOT FIXED'
        ax3.text(bar.get_x() + bar.get_width()/2., height + 0.05,
                status, ha='center', va='bottom')
    
    # 4. Summary Text
    ax4.axis('off')
    summary_text = f"""
VALIDATION SUMMARY

🚨 ORIGINAL ISSUES:
• Random Forest: {class_comparison['original_accuracy']*100:.1f}% accuracy (data leakage)
• Association Rules: {assoc_comparison['original_invalid_count']} invalid confidence values

✅ FIXES APPLIED:
• Proper train/test split before feature engineering
• Centrality computed on training subgraph only
• Confidence calculation validation and correction

📊 RESULTS:
• Classification: {class_comparison['fixed_accuracy']*100:.1f}% accuracy (realistic)
• Association Rules: {assoc_comparison['fixed_invalid_count']} invalid confidence values

🎯 CONCLUSION:
Both critical issues have been successfully resolved.
Results are now scientifically valid and publishable.
"""
    
    ax4.text(0.05, 0.95, summary_text, transform=ax4.transAxes, fontsize=11,
            verticalalignment='top', fontfamily='monospace',
            bbox=dict(boxstyle='round', facecolor='lightgray', alpha=0.8))
    
    plt.tight_layout()
    
    # Save plot
    output_path = PROJECT_ROOT / "data" / "graphs" / "fixes_validation_comparison.png"
    plt.savefig(output_path, dpi=300, bbox_inches='tight')
    print(f"Comparison visualization saved to: {output_path}")
    
    return output_path

def main():
    """主函数"""
    print("🔍 VALIDATING DATA SCIENCE FIXES")
    print("Comparing original (problematic) vs fixed (corrected) results")
    print("="*80)
    
    # Load results
    original_class, original_assoc = load_original_results()
    fixed_class, fixed_assoc = load_fixed_results()
    
    # Compare results
    class_comparison = compare_classification_results(original_class, fixed_class)
    assoc_comparison = compare_association_results(original_assoc, fixed_assoc)
    
    # Create visualization
    if class_comparison and assoc_comparison:
        viz_path = create_comparison_visualization(class_comparison, assoc_comparison)
    
    # Final summary
    print(f"\n" + "="*80)
    print("🎯 FINAL VALIDATION SUMMARY")
    print("="*80)
    
    if class_comparison and class_comparison['fix_successful']:
        print("✅ CLASSIFICATION FIX: SUCCESS")
        print(f"   Accuracy reduced from {class_comparison['original_accuracy']*100:.1f}% to {class_comparison['fixed_accuracy']*100:.1f}%")
        print("   Data leakage eliminated through proper train/test isolation")
    else:
        print("❌ CLASSIFICATION FIX: FAILED")
    
    if assoc_comparison and assoc_comparison['fix_successful']:
        print("✅ ASSOCIATION RULES FIX: SUCCESS")
        print(f"   Invalid confidence values reduced from {assoc_comparison['original_invalid_count']} to {assoc_comparison['fixed_invalid_count']}")
        print("   All confidence values now ≤ 1.0 (mathematically valid)")
    else:
        print("❌ ASSOCIATION RULES FIX: FAILED")
    
    total_fixes = sum([
        1 if class_comparison and class_comparison['fix_successful'] else 0,
        1 if assoc_comparison and assoc_comparison['fix_successful'] else 0
    ])
    
    print(f"\n🏆 OVERALL SUCCESS RATE: {total_fixes}/2 critical issues fixed")
    
    if total_fixes == 2:
        print("🎉 ALL CRITICAL ISSUES RESOLVED!")
        print("   Results are now scientifically valid and ready for publication")
    else:
        print("⚠️  Some issues remain - further investigation needed")

if __name__ == "__main__":
    main()
