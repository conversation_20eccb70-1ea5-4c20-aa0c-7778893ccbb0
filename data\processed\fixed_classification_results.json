{"method": "Fixed Enhanced Graph Classification", "data_leakage_fixed": true, "train_test_split": "Proper isolation implemented", "random_forest_results": {"train_accuracy": 1.0, "test_accuracy": 1.0, "cv_mean": 0.9666666666666667, "cv_std": 0.04714045207910316, "classification_report": {"Author": {"precision": 1.0, "recall": 1.0, "f1-score": 1.0, "support": 8.0}, "Paper": {"precision": 1.0, "recall": 1.0, "f1-score": 1.0, "support": 3.0}, "Subject": {"precision": 1.0, "recall": 1.0, "f1-score": 1.0, "support": 3.0}, "accuracy": 1.0, "macro avg": {"precision": 1.0, "recall": 1.0, "f1-score": 1.0, "support": 14.0}, "weighted avg": {"precision": 1.0, "recall": 1.0, "f1-score": 1.0, "support": 14.0}}}, "feature_engineering": {"train_nodes": 32, "test_nodes": 14, "feature_isolation": "Training centrality computed on training subgraph only", "test_features": "Local features only, no global centrality"}, "graph_stats": {"total_nodes": 46, "total_edges": 84, "train_subgraph_nodes": 32, "train_subgraph_edges": 45}, "generated_at": "2025-06-27T09:46:05.141102"}