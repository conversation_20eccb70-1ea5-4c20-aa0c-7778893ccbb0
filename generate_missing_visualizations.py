"""
Generate Missing Visualizations Script
专门生成缺失的可视化图像，补充当前只有2个图像的问题
"""

import os
import sys
from pathlib import Path
import time

# Project setup
PROJECT_ROOT = Path(__file__).parent.absolute()
sys.path.insert(0, str(PROJECT_ROOT))

def check_data_availability():
    """检查数据文件是否存在"""
    data_file = PROJECT_ROOT / "data" / "processed" / "extracted_entities_relations.json"
    if not data_file.exists():
        print(f"[ERROR] Data file not found: {data_file}")
        print("Please ensure you have run the data extraction process first.")
        return False
    
    print(f"[OK] Data file found: {data_file}")
    return True

def run_original_classification_visualization():
    """运行原始分类模块生成可视化"""
    print("\n--- Running Original Classification ---")
    
    try:
        from src.data_mining.classification import run_node_classification
        
        classifier = run_node_classification()
        if classifier:
            print("[OK] Original classification visualization generated")
            print("     Generated: classification_result.png")
            return True
        else:
            print("[ERROR] Original classification failed")
            return False
            
    except Exception as e:
        print(f"[ERROR] Error in original classification: {e}")
        return False

def run_original_association_rules_visualization():
    """运行原始关联规则模块生成可视化"""
    print("\n--- Running Original Association Rules ---")
    
    try:
        from src.data_mining.association_rules import run_frequent_subgraph_mining
        
        miner = run_frequent_subgraph_mining()
        if miner:
            print("[OK] Original association rules visualizations generated")
            print("     Generated: frequent_node_patterns.png")
            print("     Generated: frequent_edge_patterns.png")
            return True
        else:
            print("[ERROR] Original association rules failed")
            return False
            
    except Exception as e:
        print(f"[ERROR] Error in original association rules: {e}")
        return False

def run_clustering_visualizations():
    """运行聚类分析生成可视化"""
    print("\n--- Running Clustering Analysis ---")
    
    try:
        from src.data_mining.clustering import run_author_clustering, run_paper_topic_clustering
        
        # Author clustering
        print("  Running author collaboration clustering...")
        author_analyzer = run_author_clustering()
        author_success = author_analyzer is not None
        
        # Paper-topic clustering
        print("  Running paper-topic clustering...")
        topic_analyzer = run_paper_topic_clustering()
        topic_success = topic_analyzer is not None
        
        if author_success and topic_success:
            print("[OK] Clustering visualizations generated")
            print("     Generated: author_collaboration_clusters.png")
            print("     Generated: paper_topic_clusters.png")
            return True
        else:
            print(f"[PARTIAL] Clustering partially successful (Author: {author_success}, Topic: {topic_success})")
            return author_success or topic_success
            
    except Exception as e:
        print(f"[ERROR] Error in clustering analysis: {e}")
        return False

def check_current_images():
    """检查当前已有的图像"""
    print("\n--- Checking Current Images ---")
    
    graphs_dir = PROJECT_ROOT / "data" / "graphs"
    if not graphs_dir.exists():
        print("[INFO] Graphs directory does not exist, will be created")
        return []
    
    image_files = list(graphs_dir.glob("*.png"))
    
    print(f"Current images ({len(image_files)}):")
    for img_file in sorted(image_files):
        file_size = img_file.stat().st_size / 1024  # KB
        print(f"  [+] {img_file.name} ({file_size:.1f} KB)")
    
    return image_files

def generate_font_test():
    """生成字体测试图像"""
    print("\n--- Generating Font Test ---")
    
    try:
        from src.utils.font_config import test_chinese_display
        
        test_path = test_chinese_display()
        if test_path:
            print("[OK] Font test image generated")
            print("     Generated: font_test.png")
            return True
        else:
            print("[ERROR] Font test failed")
            return False
            
    except Exception as e:
        print(f"[ERROR] Error in font test: {e}")
        return False

def main():
    """主函数"""
    print("Missing Visualizations Generator")
    print("=" * 50)
    print("This script will generate the missing visualization images")
    print("that are not created by the current enhanced-only execution.")
    
    # Check data availability
    if not check_data_availability():
        return
    
    # Check current images
    initial_images = check_current_images()
    
    print(f"\n[INFO] Starting with {len(initial_images)} existing images")
    print("[INFO] Generating missing visualizations...")
    
    # Track results
    results = {}
    
    # Generate missing visualizations
    results['original_classification'] = run_original_classification_visualization()
    results['original_association'] = run_original_association_rules_visualization()
    results['clustering'] = run_clustering_visualizations()
    results['font_test'] = generate_font_test()
    
    # Check final results
    final_images = check_current_images()
    
    # Summary
    print("\n" + "=" * 50)
    print("MISSING VISUALIZATIONS GENERATION COMPLETE")
    print("=" * 50)
    
    successful_modules = sum(1 for success in results.values() if success)
    total_modules = len(results)
    
    print(f"\nModule Success Rate: {successful_modules}/{total_modules}")
    
    for module, success in results.items():
        status = "[OK]" if success else "[FAILED]"
        print(f"  {status} {module}")
    
    print(f"\nImages Before: {len(initial_images)}")
    print(f"Images After:  {len(final_images)}")
    print(f"New Images:    {len(final_images) - len(initial_images)}")
    
    if len(final_images) >= 7:
        print("\n🎉 SUCCESS: You now have a comprehensive set of visualizations!")
    else:
        print(f"\n⚠️  INFO: Generated {len(final_images)} total images.")
    
    print("\nExpected visualizations:")
    expected_images = [
        "enhanced_classification_results.png",
        "frequent_subgraphs_gspan.png", 
        "classification_result.png",
        "frequent_node_patterns.png",
        "frequent_edge_patterns.png",
        "author_collaboration_clusters.png",
        "paper_topic_clusters.png",
        "font_test.png"
    ]
    
    for expected in expected_images:
        exists = any(img.name == expected for img in final_images)
        status = "✅" if exists else "❌"
        print(f"  {status} {expected}")
    
    print(f"\nAll visualization files are saved in: {PROJECT_ROOT / 'data' / 'graphs'}")

if __name__ == "__main__":
    main()
